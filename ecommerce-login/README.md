# 电商登录页面

一个现代化、响应式的电商平台登录页面，具有完整的用户界面和交互功能。

## 功能特性

### 🎨 界面设计
- 现代化的渐变背景设计
- 响应式布局，支持移动端和桌面端
- 流畅的动画效果和过渡
- 直观的用户界面和良好的用户体验

### 🔐 登录功能
- 支持用户名、邮箱、手机号多种登录方式
- 密码显示/隐藏切换功能
- "记住我"功能，自动保存用户信息
- 完整的表单验证和错误提示

### 🚀 交互体验
- 实时表单验证
- 加载状态指示
- 消息提示系统
- 键盘快捷键支持（Enter键提交）

### 📱 社交登录
- 微信登录按钮
- QQ登录按钮
- 可扩展其他社交平台

## 文件结构

```
ecommerce-login/
├── index.html          # 主登录页面
├── styles.css          # 样式文件
├── script.js           # JavaScript功能
├── dashboard.html      # 登录成功后的演示页面
└── README.md          # 说明文档
```

## 使用方法

### 1. 直接打开
直接在浏览器中打开 `index.html` 文件即可查看登录页面。

### 2. 本地服务器
为了获得最佳体验，建议使用本地服务器：

```bash
# 使用Python
python -m http.server 8000

# 使用Node.js
npx http-server

# 使用Live Server (VS Code扩展)
右键点击index.html -> Open with Live Server
```

### 3. 测试账号
页面内置了以下测试账号：

| 用户名 | 密码 |
|--------|------|
| admin | 123456 |
| <EMAIL> | password |
| 13800138000 | 123456 |

## 功能说明

### 表单验证
- **用户名验证**：支持用户名、邮箱、手机号格式
- **密码验证**：最少6个字符
- **实时验证**：输入时实时检查格式
- **错误提示**：清晰的错误信息显示

### 记住我功能
- 勾选"记住我"后，用户名会保存到本地存储
- 下次访问时自动填充用户名
- 退出登录时可选择清除保存的信息

### 响应式设计
- **桌面端**：左右分栏布局，品牌展示区和登录表单区
- **移动端**：上下布局，优化触摸操作
- **平板端**：自适应布局调整

## 自定义配置

### 修改品牌信息
在 `index.html` 中修改以下部分：

```html
<div class="logo">
    <i class="fas fa-shopping-cart"></i>
    <h1>您的品牌名</h1>
</div>
<h2>欢迎来到您的平台</h2>
<p>您的品牌描述</p>
```

### 修改颜色主题
在 `styles.css` 中修改CSS变量：

```css
/* 主色调 */
background: linear-gradient(135deg, #您的颜色1 0%, #您的颜色2 100%);

/* 按钮颜色 */
.login-btn {
    background: linear-gradient(135deg, #您的颜色1 0%, #您的颜色2 100%);
}
```

### 集成后端API
修改 `script.js` 中的 `simulateLogin` 函数：

```javascript
async function simulateLogin(formData) {
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        return result;
    } catch (error) {
        throw new Error('网络错误');
    }
}
```

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 依赖项

- Font Awesome 6.0.0 (图标库)
- 现代浏览器的CSS Grid和Flexbox支持

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的登录功能
- 响应式设计
- 表单验证
- 社交登录按钮
