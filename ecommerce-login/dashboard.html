<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商平台 - 用户中心</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 700;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 2rem;
        }

        .welcome-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
            text-align: center;
        }

        .welcome-section h1 {
            color: #667eea;
            margin-bottom: 1rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .dashboard-card {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
        }

        .card-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .card-title {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
            color: #333;
        }

        .card-description {
            color: #666;
            line-height: 1.6;
        }

        @media (max-width: 768px) {
            .header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
            }

            .container {
                padding: 0 1rem;
            }

            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="logo">
            <i class="fas fa-shopping-cart"></i>
            <span>ShopMall</span>
        </div>
        <div class="user-info">
            <span>欢迎，<span id="username">用户</span>！</span>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> 退出登录
            </button>
        </div>
    </header>

    <div class="container">
        <div class="welcome-section">
            <h1>欢迎来到您的购物中心</h1>
            <p>在这里您可以管理您的订单、查看商品、更新个人信息等</p>
        </div>

        <div class="dashboard-grid">
            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-shopping-bag"></i>
                </div>
                <h3 class="card-title">我的订单</h3>
                <p class="card-description">查看和管理您的所有订单，跟踪配送状态</p>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-heart"></i>
                </div>
                <h3 class="card-title">我的收藏</h3>
                <p class="card-description">管理您收藏的商品，随时查看心仪的产品</p>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-user-cog"></i>
                </div>
                <h3 class="card-title">个人设置</h3>
                <p class="card-description">更新个人信息、修改密码、管理收货地址</p>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-credit-card"></i>
                </div>
                <h3 class="card-title">支付方式</h3>
                <p class="card-description">管理您的支付方式和账单信息</p>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-headset"></i>
                </div>
                <h3 class="card-title">客服支持</h3>
                <p class="card-description">联系客服，获取购物帮助和售后服务</p>
            </div>

            <div class="dashboard-card">
                <div class="card-icon">
                    <i class="fas fa-gift"></i>
                </div>
                <h3 class="card-title">优惠券</h3>
                <p class="card-description">查看可用的优惠券和促销活动</p>
            </div>
        </div>
    </div>

    <script>
        // 获取用户名并显示
        document.addEventListener('DOMContentLoaded', function() {
            const rememberedUser = localStorage.getItem('rememberedUser');
            if (rememberedUser) {
                document.getElementById('username').textContent = rememberedUser;
            }
        });

        // 退出登录功能
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                // 清除本地存储的用户信息
                localStorage.removeItem('rememberedUser');
                
                // 跳转回登录页面
                window.location.href = 'index.html';
            }
        }

        // 添加卡片点击效果
        document.querySelectorAll('.dashboard-card').forEach(card => {
            card.addEventListener('click', function() {
                const title = this.querySelector('.card-title').textContent;
                alert(`点击了：${title}\n这里应该跳转到对应的功能页面`);
            });
        });
    </script>
</body>
</html>
