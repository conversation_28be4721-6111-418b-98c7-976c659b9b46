<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电商平台 - 用户登录</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 左侧品牌展示区域 -->
        <div class="brand-section">
            <div class="brand-content">
                <div class="logo">
                    <i class="fas fa-shopping-cart"></i>
                    <h1>ShopMall</h1>
                </div>
                <h2>欢迎来到我们的购物平台</h2>
                <p>发现优质商品，享受便捷购物体验</p>
                <div class="features">
                    <div class="feature-item">
                        <i class="fas fa-truck"></i>
                        <span>免费配送</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>安全保障</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-headset"></i>
                        <span>24小时客服</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧登录表单区域 -->
        <div class="login-section">
            <div class="login-form-container">
                <div class="form-header">
                    <h2>用户登录</h2>
                    <p>请输入您的账号信息</p>
                </div>

                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="username">用户名/手机号/邮箱</label>
                        <div class="input-wrapper">
                            <i class="fas fa-user"></i>
                            <input type="text" id="username" name="username" placeholder="请输入用户名、手机号或邮箱" required>
                        </div>
                        <span class="error-message" id="usernameError"></span>
                    </div>

                    <div class="form-group">
                        <label for="password">密码</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock"></i>
                            <input type="password" id="password" name="password" placeholder="请输入密码" required>
                            <i class="fas fa-eye password-toggle" id="passwordToggle"></i>
                        </div>
                        <span class="error-message" id="passwordError"></span>
                    </div>

                    <div class="form-options">
                        <label class="remember-me">
                            <input type="checkbox" id="rememberMe">
                            <span class="checkmark"></span>
                            记住我
                        </label>
                        <a href="#" class="forgot-password">忘记密码？</a>
                    </div>

                    <button type="submit" class="login-btn" id="loginBtn">
                        <span class="btn-text">登录</span>
                        <i class="fas fa-spinner fa-spin loading-spinner" style="display: none;"></i>
                    </button>

                    <div class="divider">
                        <span>或</span>
                    </div>

                    <div class="social-login">
                        <button type="button" class="social-btn wechat-btn">
                            <i class="fab fa-weixin"></i>
                            微信登录
                        </button>
                        <button type="button" class="social-btn qq-btn">
                            <i class="fab fa-qq"></i>
                            QQ登录
                        </button>
                    </div>

                    <div class="register-link">
                        还没有账号？<a href="#" id="registerLink">立即注册</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 消息提示框 -->
    <div id="messageBox" class="message-box">
        <div class="message-content">
            <i class="message-icon"></i>
            <span class="message-text"></span>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
