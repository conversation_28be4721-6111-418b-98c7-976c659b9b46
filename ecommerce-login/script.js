// DOM元素获取
const loginForm = document.getElementById('loginForm');
const usernameInput = document.getElementById('username');
const passwordInput = document.getElementById('password');
const passwordToggle = document.getElementById('passwordToggle');
const loginBtn = document.getElementById('loginBtn');
const rememberMeCheckbox = document.getElementById('rememberMe');
const messageBox = document.getElementById('messageBox');

// 错误信息元素
const usernameError = document.getElementById('usernameError');
const passwordError = document.getElementById('passwordError');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否有记住的用户信息
    loadRememberedUser();
    
    // 绑定事件监听器
    bindEventListeners();
    
    // 输入框焦点效果
    addInputFocusEffects();
});

// 绑定事件监听器
function bindEventListeners() {
    // 表单提交
    loginForm.addEventListener('submit', handleLogin);
    
    // 密码显示/隐藏切换
    passwordToggle.addEventListener('click', togglePasswordVisibility);
    
    // 输入验证
    usernameInput.addEventListener('blur', validateUsername);
    passwordInput.addEventListener('blur', validatePassword);
    
    // 实时清除错误信息
    usernameInput.addEventListener('input', () => clearError('username'));
    passwordInput.addEventListener('input', () => clearError('password'));
    
    // 社交登录按钮
    document.querySelector('.wechat-btn').addEventListener('click', () => handleSocialLogin('wechat'));
    document.querySelector('.qq-btn').addEventListener('click', () => handleSocialLogin('qq'));
    
    // 注册链接
    document.getElementById('registerLink').addEventListener('click', handleRegisterClick);
    
    // 忘记密码链接
    document.querySelector('.forgot-password').addEventListener('click', handleForgotPassword);
}

// 处理登录表单提交
async function handleLogin(e) {
    e.preventDefault();
    
    // 验证表单
    if (!validateForm()) {
        return;
    }
    
    // 显示加载状态
    setLoadingState(true);
    
    try {
        // 获取表单数据
        const formData = {
            username: usernameInput.value.trim(),
            password: passwordInput.value,
            rememberMe: rememberMeCheckbox.checked
        };
        
        // 模拟API调用
        const result = await simulateLogin(formData);
        
        if (result.success) {
            // 登录成功
            showMessage('登录成功！正在跳转...', 'success');
            
            // 保存用户信息（如果选择记住我）
            if (formData.rememberMe) {
                saveUserInfo(formData.username);
            } else {
                clearSavedUserInfo();
            }
            
            // 延迟跳转到主页
            setTimeout(() => {
                // 这里应该跳转到实际的主页
                window.location.href = '/dashboard';
            }, 1500);
            
        } else {
            // 登录失败
            showMessage(result.message || '登录失败，请检查用户名和密码', 'error');
        }
        
    } catch (error) {
        console.error('登录错误:', error);
        showMessage('网络错误，请稍后重试', 'error');
    } finally {
        setLoadingState(false);
    }
}

// 模拟登录API调用
function simulateLogin(formData) {
    return new Promise((resolve) => {
        setTimeout(() => {
            // 模拟登录逻辑
            const validUsers = [
                { username: 'admin', password: '123456' },
                { username: '<EMAIL>', password: 'password' },
                { username: '13800138000', password: '123456' }
            ];
            
            const user = validUsers.find(u => 
                u.username === formData.username && u.password === formData.password
            );
            
            if (user) {
                resolve({ success: true, user: user });
            } else {
                resolve({ success: false, message: '用户名或密码错误' });
            }
        }, 1500); // 模拟网络延迟
    });
}

// 表单验证
function validateForm() {
    let isValid = true;
    
    if (!validateUsername()) {
        isValid = false;
    }
    
    if (!validatePassword()) {
        isValid = false;
    }
    
    return isValid;
}

// 验证用户名
function validateUsername() {
    const username = usernameInput.value.trim();
    
    if (!username) {
        showError('username', '请输入用户名、手机号或邮箱');
        return false;
    }
    
    if (username.length < 3) {
        showError('username', '用户名至少需要3个字符');
        return false;
    }
    
    // 简单的邮箱格式验证
    if (username.includes('@')) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(username)) {
            showError('username', '请输入有效的邮箱地址');
            return false;
        }
    }
    
    // 简单的手机号格式验证
    if (/^\d+$/.test(username)) {
        if (username.length !== 11) {
            showError('username', '请输入有效的手机号');
            return false;
        }
    }
    
    clearError('username');
    return true;
}

// 验证密码
function validatePassword() {
    const password = passwordInput.value;
    
    if (!password) {
        showError('password', '请输入密码');
        return false;
    }
    
    if (password.length < 6) {
        showError('password', '密码至少需要6个字符');
        return false;
    }
    
    clearError('password');
    return true;
}

// 显示错误信息
function showError(field, message) {
    const errorElement = document.getElementById(field + 'Error');
    const inputElement = document.getElementById(field);
    
    errorElement.textContent = message;
    inputElement.style.borderColor = '#e74c3c';
}

// 清除错误信息
function clearError(field) {
    const errorElement = document.getElementById(field + 'Error');
    const inputElement = document.getElementById(field);
    
    errorElement.textContent = '';
    inputElement.style.borderColor = '#e1e5e9';
}

// 切换密码显示/隐藏
function togglePasswordVisibility() {
    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordInput.setAttribute('type', type);
    
    // 切换图标
    if (type === 'text') {
        passwordToggle.classList.remove('fa-eye');
        passwordToggle.classList.add('fa-eye-slash');
    } else {
        passwordToggle.classList.remove('fa-eye-slash');
        passwordToggle.classList.add('fa-eye');
    }
}

// 设置加载状态
function setLoadingState(loading) {
    const btnText = loginBtn.querySelector('.btn-text');
    const spinner = loginBtn.querySelector('.loading-spinner');
    
    if (loading) {
        btnText.textContent = '登录中...';
        spinner.style.display = 'inline-block';
        loginBtn.disabled = true;
    } else {
        btnText.textContent = '登录';
        spinner.style.display = 'none';
        loginBtn.disabled = false;
    }
}

// 显示消息提示
function showMessage(message, type = 'info') {
    const messageText = messageBox.querySelector('.message-text');
    
    messageText.textContent = message;
    messageBox.className = `message-box ${type}`;
    messageBox.classList.add('show');
    
    // 3秒后自动隐藏
    setTimeout(() => {
        messageBox.classList.remove('show');
    }, 3000);
}

// 处理社交登录
function handleSocialLogin(platform) {
    showMessage(`正在跳转到${platform === 'wechat' ? '微信' : 'QQ'}登录...`, 'info');
    
    // 这里应该跳转到对应的社交登录页面
    setTimeout(() => {
        console.log(`跳转到${platform}登录`);
    }, 1000);
}

// 处理注册链接点击
function handleRegisterClick(e) {
    e.preventDefault();
    showMessage('正在跳转到注册页面...', 'info');
    
    // 这里应该跳转到注册页面
    setTimeout(() => {
        window.location.href = '/register';
    }, 1000);
}

// 处理忘记密码
function handleForgotPassword(e) {
    e.preventDefault();
    showMessage('正在跳转到密码重置页面...', 'info');
    
    // 这里应该跳转到密码重置页面
    setTimeout(() => {
        window.location.href = '/forgot-password';
    }, 1000);
}

// 保存用户信息到本地存储
function saveUserInfo(username) {
    localStorage.setItem('rememberedUser', username);
}

// 清除保存的用户信息
function clearSavedUserInfo() {
    localStorage.removeItem('rememberedUser');
}

// 加载记住的用户信息
function loadRememberedUser() {
    const rememberedUser = localStorage.getItem('rememberedUser');
    if (rememberedUser) {
        usernameInput.value = rememberedUser;
        rememberMeCheckbox.checked = true;
    }
}

// 添加输入框焦点效果
function addInputFocusEffects() {
    const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
    
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
}

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Enter键提交表单
    if (e.key === 'Enter' && (usernameInput === document.activeElement || passwordInput === document.activeElement)) {
        e.preventDefault();
        loginForm.dispatchEvent(new Event('submit'));
    }
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // 页面重新可见时，清除可能的错误状态
        clearError('username');
        clearError('password');
    }
});
