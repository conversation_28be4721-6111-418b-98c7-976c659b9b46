/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* 主容器 */
.container {
    display: flex;
    min-height: 100vh;
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* 左侧品牌区域 */
.brand-section {
    flex: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
    overflow: hidden;
}

.brand-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.brand-content {
    text-align: center;
    z-index: 1;
    position: relative;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;
}

.logo i {
    font-size: 3rem;
    margin-right: 1rem;
    color: #fff;
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -1px;
}

.brand-content h2 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 300;
}

.brand-content p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 3rem;
}

.features {
    display: flex;
    justify-content: space-around;
    max-width: 400px;
    margin: 0 auto;
}

.feature-item {
    text-align: center;
    opacity: 0.9;
}

.feature-item i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.feature-item span {
    font-size: 0.9rem;
}

/* 右侧登录区域 */
.login-section {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    background: #f8f9fa;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
    background: white;
    padding: 3rem 2.5rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h2 {
    font-size: 2rem;
    color: #333;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-header p {
    color: #666;
    font-size: 0.95rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #555;
    font-weight: 500;
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: 1rem;
    color: #999;
    z-index: 1;
}

.input-wrapper input {
    width: 100%;
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fff;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.password-toggle {
    position: absolute;
    right: 1rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
}

.error-message {
    display: block;
    color: #e74c3c;
    font-size: 0.8rem;
    margin-top: 0.5rem;
    min-height: 1rem;
}

/* 表单选项 */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 0.9rem;
    color: #666;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 0.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: -2px;
    left: 2px;
    color: white;
    font-size: 12px;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* 登录按钮 */
.login-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.loading-spinner {
    margin-left: 0.5rem;
}

/* 分割线 */
.divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
    color: #999;
    font-size: 0.9rem;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e5e9;
    z-index: 0;
}

.divider span {
    background: white;
    padding: 0 1rem;
    position: relative;
    z-index: 1;
}

/* 社交登录 */
.social-login {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.social-btn {
    flex: 1;
    padding: 0.8rem;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.wechat-btn {
    color: #1aad19;
    border-color: #1aad19;
}

.wechat-btn:hover {
    background: #1aad19;
    color: white;
}

.qq-btn {
    color: #12b7f5;
    border-color: #12b7f5;
}

.qq-btn:hover {
    background: #12b7f5;
    color: white;
}

/* 注册链接 */
.register-link {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

.register-link a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.register-link a:hover {
    color: #5a6fd8;
    text-decoration: underline;
}

/* 消息提示框 */
.message-box {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 1000;
    display: flex;
    align-items: center;
    min-width: 300px;
}

.message-box.show {
    transform: translateX(0);
}

.message-content {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.message-icon {
    font-size: 1.2rem;
}

.message-box.success {
    border-left: 4px solid #27ae60;
}

.message-box.success .message-icon::before {
    content: '✓';
    color: #27ae60;
}

.message-box.error {
    border-left: 4px solid #e74c3c;
}

.message-box.error .message-icon::before {
    content: '✕';
    color: #e74c3c;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
        min-height: auto;
    }
    
    .brand-section {
        padding: 2rem 1rem;
        min-height: 40vh;
    }
    
    .brand-content h2 {
        font-size: 1.5rem;
    }
    
    .features {
        flex-direction: column;
        gap: 1rem;
    }
    
    .login-section {
        padding: 1rem;
    }
    
    .login-form-container {
        padding: 2rem 1.5rem;
    }
    
    .social-login {
        flex-direction: column;
    }
    
    .message-box {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        min-width: auto;
    }
}
