# LightCode消息服务器

LightCode插件内置进程服务。

## 项目配置说明

### 基本信息
- `name`: lightcode-process - 项目名称
- `version`: 1.0.0 - 项目版本号
- `description`: LightCode插件内置进程服务 - 项目描述
- `main`: lightcode.js - 主入口文件
- `bin`: lightcode.js - 可执行文件入口

### 脚本命令
- `start`: 启动生产环境服务
  ```bash
  node dist/server/index.js
  ```
- `dev`: 开发环境调试运行
  ```bash
  ts-node --inspect-brk --transpile-only src/index.ts
  ```
- `build`: 构建项目
  ```bash
  tsc && webpack --config webpack.config.ts
  ```
- `clean`: 清理构建目录
  ```bash
  rimraf dist
  ```
- `type-check`: TypeScript 类型检查
  ```bash
  tsc --noEmit
  ```
- `package`: 打包可执行文件
  ```bash
  npm run clean && npm run build && npx pkg dist/lightcode.js --target node18-win-x64 --output dist/lightcode.exe --no-progress --options no-warnings --public-packages "*" --public --no-console --no-bytecode --config package.json
  ```

### 生产环境依赖
- `@anush008/tokenizers`: 分词器
- `@anush008/tokenizers-*`: 各平台分词器
- `@grpc/grpc-js`: gRPC JavaScript 实现
- `@grpc/proto-loader`: gRPC proto 文件加载器
- `@modelcontextprotocol/sdk`: 模型上下文协议 SDK
- `abort-controller`: 请求中止控制器
- `axios`: HTTP 客户端
- `chokidar`: 文件监视器
- `fast-deep-equal`: 深度对象比较
- `glob`: 文件模式匹配
- `lightcode-process`: 进度管理包
- `iconv-lite`: 字符编码转换
- `node-fetch`: Node.js 的 fetch 实现
- `tiktoken`: OpenAI 的 token 计算器
- `zod`: 运行时类型验证

### 开发环境依赖
- `@babel/*`: Babel 相关工具
  - `@babel/core`: Babel 核心
  - `@babel/plugin-transform-runtime`: Babel 运行时转换
  - `@babel/preset-env`: Babel 环境预设
  - `@babel/preset-typescript`: Babel TypeScript 预设
  - `@babel/runtime`: Babel 运行时
- `@types/*`: TypeScript 类型定义
- `babel-loader`: Webpack Babel 加载器
- `copy-webpack-plugin`: Webpack 文件复制插件
- `rimraf`: 跨平台文件删除工具
- `ts-node`: TypeScript 执行环境
- `tsconfig-paths`: TypeScript 路径解析
- `typescript`: TypeScript 编译器
- `webpack`: Webpack 打包工具
- `webpack-cli`: Webpack 命令行工具
- `webpack-node-externals`: Webpack Node.js 外部依赖处理

### pkg 打包配置
- `assets`: 需要打包的资源文件
  - 包括各种依赖模块和资源文件
- `scripts`: 需要打包的脚本文件
  - `dist/lightcode.js`
- `targets`: 目标平台
  - `node18-win-x64`
- `options`: 打包选项
  - `no-warnings`: 不显示警告
  - `public-packages="*"`: 公开所有包
  - `public`: 公开模式

## 使用说明

1. 安装依赖
```bash
npm install --force
```

2. 开发环境运行
```bash
npm run dev
```

3. 构建项目
```bash
npm run build
```

4. 打包可执行文件
```bash
npm run package
```

5. 启动生产环境服务
```bash
npm start
```

# LightCode Progress 打包说明

## 打包命令详解

打包命令使用 `pkg` 工具将 Node.js 应用打包成可执行文件。以下是命令的详细说明：

```bash
npm run clean && npm run build && npx pkg dist/lightcode.js --target node18-win-x64 --output dist/lightcode.exe --no-progress --options no-warnings --public-packages "*" --public --no-console --no-bytecode --config package.json
```

### 命令分解说明

1. **前置命令**
   - `npm run clean`: 清理之前的构建文件
   - `npm run build`: 执行 TypeScript 编译和 webpack 打包

2. **pkg 打包参数**
   - `dist/lightcode.js`: 指定入口文件
   - `--target node18-win-x64`: 指定目标平台为 Windows x64，Node.js 18 版本
   - `--output dist/lightcode.exe`: 指定输出文件路径和名称
   - `--no-progress`: 不显示打包进度条
   - `--options no-warnings`: 不显示警告信息
   - `--public-packages "*"`: 将所有依赖包标记为公共包
   - `--public`: 使所有文件可访问
   - `--no-console`: 不显示控制台窗口
   - `--no-bytecode`: 不生成字节码
   - `--config package.json`: 使用 package.json 中的配置

### package.json 中的 pkg 配置

在 package.json 中定义了以下 pkg 相关配置：

1. **assets**: 需要包含在可执行文件中的资源文件
   - 包含各种依赖模块的源代码

2. **scripts**: 需要打包的脚本文件
   - 包含主入口文件 `dist/lightcode.js`

3. **targets**: 目标平台
   - 当前仅支持 Windows x64 平台

4. **options**: 打包选项
   - 不显示警告
   - 所有包设为公共包
   - 文件设为可访问

### 使用说明

1. 确保已安装所有依赖：
   ```bash
   npm install --force
   ```

2. 执行打包命令：
   ```bash
   npm run package
   ```

3. 打包完成后，可执行文件将生成在 `dist/lightcode.exe` 

4.当前打包指令只针对Windows环境，打包时，请确保当前用户目录下存在：\.pkg-cache\v3.4\fetched-v18.5.0-win-x64