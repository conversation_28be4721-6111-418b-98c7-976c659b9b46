{"name": "lightcode-process", "version": "1.0.0", "description": "LightCode插件内置进程服务", "main": "lightcode.js", "bin": "dist/lightcode.js", "scripts": {"build": "tsc && webpack --config webpack.config.ts", "clean": "<PERSON><PERSON><PERSON> dist", "type-check": "tsc --noEmit", "package": "npm run clean && npm run build && npx pkg dist/lightcode.js --target node18-win-x64 --output dist/lightcode.exe --no-progress --options no-warnings --public-packages \"*\" --public --no-console --no-bytecode --config package.json && del dist\\lightcode.js", "build:mac": "pkg . --targets node18-macos-x64 --output dist/light_code_mac"}, "dependencies": {"@anush008/tokenizers": "0.0.0", "@anush008/tokenizers-darwin-universal": "^0.0.0", "@grpc/grpc-js": "^1.13.1", "@grpc/proto-loader": "^0.7.10", "@modelcontextprotocol/sdk": "^1.9.0", "abort-controller": "^3.0.0", "axios": "^1.8.4", "chokidar": "^4.0.3", "fast-deep-equal": "^3.1.3", "glob": "^11.0.0", "iconv-lite": "^0.6.3", "image-size": "^2.0.2", "lightcode-process": "file:", "node-fetch": "^2.6.9", "pkg": "^5.8.1", "tiktoken": "^1.0.18", "zod": "^3.24.2"}, "devDependencies": {"@babel/core": "^7.23.6", "@babel/plugin-transform-runtime": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/preset-typescript": "^7.23.3", "@babel/runtime": "^7.23.6", "@types/chokidar": "^1.7.5", "@types/glob": "^8.1.0", "@types/node": "^20.17.27", "@types/node-fetch": "^2.6.4", "@types/vscode": "^1.99.1", "@types/webpack-node-externals": "^3.0.4", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^13.0.0", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.2", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-node-externals": "^3.0.0"}, "pkg": {"scripts": "dist/lightcode.js", "assets": ["resources/**/*"], "targets": ["node18-macos-x64"], "outputPath": "dist"}}