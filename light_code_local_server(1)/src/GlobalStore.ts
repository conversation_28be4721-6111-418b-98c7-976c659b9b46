// 全局变量，进程启动之后不可修改的变量
export const PORT = "port"; // 端口
export const LOG_DIR = "logDir"; // 日志目录
export const DEBUG = "debug"; // 是否开启调试
export const HEART_BEAT_INTERVAL = "heartBeatInterval"; // 心跳间隔,单位毫秒
export const IDE_VERSION = "ideVersion"; // IDE，拉起进程的ide的版本
export const LIGHT_CODE_VERSION = "lightCodeVersion"; // LightCode，拉起进程的LightCode的版本
export const PROCESS_VERSION = "processVersion"; // 进程，拉起进程的进程的版本
export const VERSION = "1.0.0"; // 版本

// 全局变量，进程启动之后可修改的变量
export const DEFAULT_AGENTS = "defaultAgents"; // 默认代理
export const DEFAULT_TOOLS = "defaultTools"; // 默认工具
export const DEFAULT_TOOL_DEFINES = "defaultToolDefines"; // 默认工具定义
export const IDE_CLIENT = "ideClient"; // 客户端

export class GlobalStore {
  private static store: GlobalStore;

  private static staticValue: { [key: string]: any } = {};

  private constructor() { }

  // 获取全局数据管理器
  static getStore() {
    if (GlobalStore.store == null) {
      GlobalStore.store = new GlobalStore();
      GlobalStore.store.setStaticValue(PROCESS_VERSION, VERSION);
      GlobalStore.store.setStaticValue(PORT, 51515);
      GlobalStore.store.setStaticValue(LOG_DIR, process.cwd());
      GlobalStore.store.setStaticValue(DEBUG, false);

      GlobalStore.store.setStaticValue(DEFAULT_AGENTS, new Map());
      GlobalStore.store.setStaticValue(DEFAULT_TOOL_DEFINES, new Map());
      GlobalStore.store.setStaticValue(IDE_CLIENT, new Map());
    }
    return GlobalStore.store;
  }

  // 设置实例变量
  setStaticValue(key: string, value: any) {
    GlobalStore.staticValue[key] = value;
  }

  // 获取实例变量
  getStaticValue(key: string) {
    return GlobalStore.staticValue[key];
  }
}
