import { McpToolDefine } from "./McpToolDefine";
import { AgentToolDefine } from "./AgentToolDefine";

export enum AgentType {
    system = "system", custom = "custom"
}
export interface AgentToken {
    model: string;
    wordlistName: string;
    tokens: number;
}
export class AgentDefine {
    id: string;
    //英文名称
    name: string;
    // 中文名称
    title: string;
    // 描述
    description: string;
    //工具名称列表
    baseToolDefines?: AgentToolDefine[];
    mcpToolDefines?: McpToolDefine[];
    //默认系统Prompt
    systemPrompt: string;
    //类型
    type: AgentType;
    // 工具token消耗
    tokenCosts?: AgentToken[];

    constructor(
        id: string,
        type: AgentType,
        name: string,
        title: string,
        description?: string,
        systemPrompt?: string,
        baseToolDefines?: AgentToolDefine[],
        mcpToolDefines?: McpToolDefine[],
        tokenCosts?: AgentToken[],
    ) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.title = title;
        this.description = description || "";
        this.systemPrompt = systemPrompt || "";
        this.baseToolDefines = baseToolDefines;
        this.mcpToolDefines = mcpToolDefines;
        this.tokenCosts = tokenCosts || [];
    }
}