import { AGENT_SAVE } from '../consts/Urls';
import { MessageRequest, Tool } from '../model/LLModel';
import { AgentChatMessage, request } from '../request/request';
import { logger } from '../utils/Logger';
import { TokenUtils } from '../utils/tokenUtil';
import { formatDateTime, generateUUID } from '../utils/util';
import { AgentMessage } from './agentRouteStream';

interface AgentDisplayMessage {
    user_account?: string;
    display_message_id?: string;
    content: AgentDisplayContent[];
    sent?: boolean;
    order_num?: number;
    pre_display_message_id?: string;
    chat_role?: string;
    agent_conv_id?: string;
    request_time?: string;
    prompt_type?: string;
    model_name?: string;
    model_params?: string;
    terminal?: string;
    terminal_version?: string;
    response_time?: string;
    first_edit_time?: string;
    first_response_time?: string;
    last_response_time?: string;
}
export interface AgentDisplayContent {
    type: string;
    content: string;
    toolId?: string;
}

interface AgentModelMessage {
    model_message_id: string;
    request_time?: string;
    display_message_id: string;
    agent_conv_id?: string;
    message_role: string;
    content: string;
    sent?: boolean;
    order_num?: number;
    biz_type: string;
    pre_model_message_id?: string;
    user_account?: string;
}

export interface CodeInfo {
    display_message_id: string;
    file_name: string;
    method: string;
    method_signature: string;
    vcs_path: string;
    branch: string;
    sent: boolean;
}
export interface EditFileCache {
    filePath: string;
    originFileCachePath: string;
    diffCount: number;
}
class AgentStorage {
    private type: string;
    private taskId: string;
    private display_messages: AgentDisplayMessage[] = [];
    private model_messages: AgentModelMessage[] = [];
    private currentSelect: string;
    private currentLine: number;
    private currentOpen: string[];
    private agent_conv_id: string;
    private user_account: string;
    private innerTask: any[];
    private agent_id: string;
    private extraSystemPrompts: string[]
    private origiFileContentMap: Map<string, EditFileCache>;
    private multimodal: boolean = false;
    private codeInfos: CodeInfo[] = [];

    constructor(
        taskId: string,
        currentSelect: string,
        currentLine: number,
        currentOpen: string[],
        agentConvId: string,
        userAccount: string,
        agentId: string,
        extraSystemPrompts: string[],
        type: string,
        multimodal: boolean
    ) {
        this.taskId = taskId;
        this.currentSelect = currentSelect;
        this.currentLine = currentLine;
        this.currentOpen = currentOpen;
        this.agent_conv_id = agentConvId;
        this.user_account = userAccount;
        this.agent_id = agentId;
        this.innerTask = [];
        this.extraSystemPrompts = extraSystemPrompts;
        this.origiFileContentMap = new Map();
        this.multimodal = multimodal;
        this.type = type;
    }
    public setTaskId(taskId: string) {
        this.taskId = taskId;
    }
    public setType(promptType: string) {
        this.type = promptType;
    }
    public getType(): string {
        return this.type;
    }
    public getMultimodal(): boolean {
        return this.multimodal;
    }
    public setMultimodal(multimodal: boolean) {
        if (multimodal) {
            this.multimodal = multimodal;
        }
    }
    public addCodeInfo(codeInfo: CodeInfo) {
        this.codeInfos.push(codeInfo);
    }
    public addInnerTask(cancel: () => void) {
        this.innerTask.push(cancel);
    }
    public removeInnerTask(cancel: () => void) {
        // 删除元素
        this.innerTask = this.innerTask.filter((item) => item !== cancel);
    }
    public getOrigiFileContentMap() {
        return this.origiFileContentMap;
    }
    public clearInnerTask() {
        this.innerTask?.forEach((cancel) => cancel());
        this.innerTask = [];
    }

    public getTaskId(): string {
        return this.taskId;
    }

    public setDisplayMessages(displayMessages: AgentDisplayMessage[]) {
        this.display_messages = displayMessages;
    }

    public setModelMessages(modelMessages: AgentModelMessage[]) {
        this.model_messages = modelMessages;
    }

    public getCurrentSelect(): string {
        return this.currentSelect;
    }

    public getCurrentLine(): number {
        return this.currentLine;
    }

    public getCurrentOpen(): string[] {
        return [...this.currentOpen];
    }

    public getExtraSystemPrompts(): string[] {
        return [...this.extraSystemPrompts];
    }

    public setCurrentSelect(select: string): void {
        this.currentSelect = select;
    }

    public setCurrentLine(line: number): void {
        this.currentLine = line;
    }

    public setCurrentOpen(open: string[]): void {
        this.currentOpen = [...open];
    }

    public setExtraSystemPrompts(extraSystemPrompts: string[]): void {
        this.extraSystemPrompts = [...extraSystemPrompts];
    }

    public getAgentConvId(): string {
        return this.agent_conv_id;
    }

    public getAgentId(): string {
        return this.agent_id;
    }

    public getUserAccount(): string {
        return this.user_account;
    }

    public getDisplayMessages(): AgentDisplayMessage[] {
        return [...this.display_messages];
    }

    public getModelMessages(): AgentModelMessage[] {
        return [...this.model_messages];
    }

    /**
     * 获取消息
     * 对图片资源的裁剪，当前实现要求多模态场景下，仅允许有一张图片
     * @returns 消息列表
     */
    public getMessagesWithOneImage(): AgentModelMessage[] {
        const messages: AgentModelMessage[] = [];
        let lastImageIndex = -1;
        // 如果存在图片资源，则先找到最后一张图片的下标
        this.model_messages.forEach((item, idx) => {
            if (this.multimodal && item.biz_type === 'image_url') {
                lastImageIndex = idx;
            }
        });
        this.model_messages.forEach((item, idx) => {
            if (item.biz_type === 'image_url') {
                // 仅保留最后一张图片
                if (idx === lastImageIndex) {
                    messages.push(item);
                }
            } else {
                messages.push(item);
            }
        });
        return messages;
    }

    public transToModelMessages(agentModelMessages: AgentModelMessage[]): MessageRequest[] {
        const messages: MessageRequest[] = [];
        agentModelMessages.forEach((item) => {
            if (this.multimodal) {
                // 对于多模态场景，主要的区别就是消息体中的content不是一个字符串而是一个复杂对象数组
                const { role, content, tool_calls, tool_call_id } = JSON.parse(item.content);
                if (item.biz_type === 'image_url') {
                    messages.push({ role: role, content: [{ type: 'image_url', image_url: { url: content, detail: 'auto' } }] });
                } else if (role === 'tool') {
                    messages.push({ role: role, content: [{ type: 'text', text: content }], tool_call_id: tool_call_id });
                } else if (tool_calls && tool_calls.length > 0) {
                    messages.push({ role: role, content: [{ type: 'text', text: content }], tool_calls: tool_calls });
                } else {
                    messages.push({ role: role, content: [{ type: 'text', text: content }] });
                }
            } else {
                messages.push(JSON.parse(item.content));
            }
        });
        return messages;
    }

    public async getFinalCompletion(agentMessage: AgentMessage, biostorm: boolean, toolsDes: Tool[]): Promise<AgentChatMessage> {
        if (this.multimodal) {
            // 20250715,需求202506253580，图文问答解除1张图片限制
            const messages = await TokenUtils.calculateMultimodalMessages(this.getModelMessages(), agentMessage.wordlistName, agentMessage.imageAlgoType, agentMessage.requestTokenLimit);
            const finalMessages = this.transToModelMessages(messages);
            return {
                prompt_type: agentMessage.promptType,
                model_name: agentMessage.model,
                multimodal: this.multimodal,
                multimodal_completion: {
                    messages: finalMessages,
                    ...(!biostorm && toolsDes && toolsDes.length > 0 ? { tools: toolsDes } : {}),
                    model: agentMessage.model,
                    stream: true
                }
            }
        } else {
            const messages = await TokenUtils.calculateMessages(this.getModelMessages(), agentMessage.wordlistName, agentMessage.requestTokenLimit);
            const finalMessages = this.transToModelMessages(messages);
            return {
                prompt_type: agentMessage.promptType,
                model_name: agentMessage.model,
                multimodal: this.multimodal,
                completion: {
                    messages: finalMessages,
                    model: agentMessage.model,
                    ...(!biostorm && toolsDes && toolsDes.length > 0 ? { tools: toolsDes } : {}),
                    stream: true
                }
            }
        }
    }

    unSendDisplayMessage() {
        const unSend = [];
        for (const message of this.display_messages) {
            if (!message.sent) {
                message.sent = true;
                // content需要转换为string
                unSend.push({
                    display_message_id: message.display_message_id,
                    pre_display_message_id: message.pre_display_message_id,
                    content: JSON.stringify(message.content),
                    order_num: message.order_num,
                    agent_conv_id: message.agent_conv_id,
                    chat_role: message.chat_role,
                    request_time: message.request_time,
                    prompt_type: message.prompt_type,
                    model_name: message.model_name,
                    model_params: message.model_params,
                    terminal: message.terminal,
                    terminal_version: message.terminal_version,
                    response_time: message.response_time,
                    first_edit_time: message.first_edit_time,
                    first_response_time: message.first_response_time,
                    last_response_time: message.last_response_time,
                    user_account: message.user_account,
                });
            }
        }
        return unSend;
    }
    unSendModelMessage() {
        const unSend: AgentModelMessage[] = [];
        for (const message of this.model_messages) {
            if (!message.sent) {
                message.sent = true;
                unSend.push(message);
            }
        }
        return unSend;
    }
    unSendCodeInfo() {
        const unSend: CodeInfo[] = [];
        for (const codeInfo of this.codeInfos) {
            if (!codeInfo.sent) {
                codeInfo.sent = true;
                unSend.push(codeInfo);
            }
        }
        return unSend;
    }
    appendChat(agentDisplayMessage: AgentDisplayMessage): void {
        agentDisplayMessage.user_account = this.user_account;
        agentDisplayMessage.agent_conv_id = this.agent_conv_id;
        agentDisplayMessage.request_time = formatDateTime();
        agentDisplayMessage.prompt_type = this.type;
        // agentDisplayMessage.model_params = this.model_params;
        // agentDisplayMessage.response_time = formatDateTime();
        // agentDisplayMessage.first_edit_time = formatDateTime();
        if (this.display_messages.length === 0) {
            agentDisplayMessage.order_num = 1;
            this.display_messages.push(agentDisplayMessage);
        } else {
            const last = this.display_messages[this.display_messages.length - 1];
            if (last.display_message_id === agentDisplayMessage.display_message_id) {
                last.content = [...last.content, ...agentDisplayMessage.content];
            } else {
                // messageId不同，说明是新的对话。如果有历史对话，则当前对话的前一个对话的ID是最后一个历史对话的ID
                agentDisplayMessage.pre_display_message_id = last.display_message_id;
                agentDisplayMessage.order_num = last.order_num ? last.order_num + 1 : 1;
                this.display_messages.push(agentDisplayMessage);
            }
        }
    }
    appendMessage(message: MessageRequest, displayMessageId: string, bizType?: string): void {
        const modelMessage: AgentModelMessage = {
            model_message_id: generateUUID(),
            display_message_id: displayMessageId,
            message_role: message.role,
            content: JSON.stringify(message),
            biz_type: bizType || '',
        };
        this.appendProcess(modelMessage);
    }
    appendProcess(agentModelMessage: AgentModelMessage): void {
        agentModelMessage.user_account = this.user_account;
        agentModelMessage.agent_conv_id = this.agent_conv_id;
        agentModelMessage.request_time = formatDateTime();
        agentModelMessage.sent = false;
        if (this.model_messages.length === 0) {
            agentModelMessage.order_num = 1;
        } else {
            const pre = this.model_messages[this.model_messages.length - 1];
            agentModelMessage.pre_model_message_id = pre.model_message_id;
            agentModelMessage.order_num = pre.order_num ? pre.order_num + 1 : 1;
        }
        this.model_messages.push(agentModelMessage);
    }
    removeMessage(i: number): void {
        this.model_messages.splice(i, 1);
    }

    /**
     * 注意：数据组装逻辑不能异步处理，否则会将部分ai的数据提前组装存储了(图片数据)，导致数据不完整
     * @param baseUrl 
     * @returns 
     */
    saveAgent(baseUrl: string) {
        // 需要确保数据的正确性，agent模式下，大模型要求toolId必须是闭环的
        // 因为存在用户中间停止回答或者其它异常情况导致数据不完整，此时为了保障该会话的可用性，需要删除掉toolId没有响应映射的数据
        // this.formatMessage();

        const unSendDisplayMessages = this.unSendDisplayMessage();
        const unSendModelMessages = this.unSendModelMessage();
        if (unSendDisplayMessages.length === 0 && unSendModelMessages.length === 0) {
            return;
        }
        const body = JSON.stringify({
            user_account: this.getUserAccount(),
            agent_conv_id: this.getAgentConvId(),
            agent_id: this.getAgentId(),
            multimodal: this.multimodal,
            code_infos: this.unSendCodeInfo(),
            display_messages: unSendDisplayMessages,
            model_messages: unSendModelMessages
        });
        setImmediate(() => {
            request(baseUrl + AGENT_SAVE, body)
                .catch((error: Error) => {
                    logger.error('保存agent会话失败', error instanceof Error ? error : new Error(String(error)));
                    const { display_messages, model_messages } = JSON.parse(body) as {
                        display_messages: [];
                        model_messages: AgentModelMessage[];
                    };
                    this.getDisplayMessages().forEach((item: AgentDisplayMessage) => {
                        display_messages.forEach((displayItem: AgentDisplayMessage) => {
                            if (displayItem.display_message_id === item.display_message_id) {
                                item.sent = false;
                            }
                        });
                    });
                    this.getModelMessages().forEach((item: AgentModelMessage) => {
                        model_messages.forEach((modelItem: AgentModelMessage) => {
                            if (modelItem.model_message_id === item.model_message_id) {
                                item.sent = false;
                            }
                        });
                    });
                });
        });
    }

    formatMessage() {
        // 倒序遍历 modelMessages,如果最后一条消息角色是assistant，则需要检查tool_calls是否存在，如果不存在，则需要移除掉这条数据
        for (let i = this.model_messages.length - 1; i >= 0; i--) {
            const item = this.model_messages[i];
            if (item.message_role === 'assistant') {
                const { tool_calls } = JSON.parse(item.content);
                if (tool_calls && tool_calls.length > 0) {
                    this.model_messages.splice(i, 1);
                }
            }
        }
    }
}

export { AgentDisplayMessage, AgentModelMessage, AgentStorage };