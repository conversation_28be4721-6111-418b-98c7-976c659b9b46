import { Tool } from "../model/LLModel";

export class AgentToolDefine {
    name: string;
    source: string;
    autoExec: boolean;
    displayName?: string;
    tool?: Tool; // 工具定义,只有在详情查询时，才会包含值
    constructor(
        name: string,
        source: string,
        autoExec: boolean,
        displayName?: string,
    ) {
        this.name = name;
        this.source = source;
        this.displayName = displayName || name;
        this.autoExec = autoExec;
    }
}
