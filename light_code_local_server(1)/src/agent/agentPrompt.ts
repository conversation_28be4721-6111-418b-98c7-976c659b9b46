import path from "path";
import { ResourceTypeParam } from "../resource/ResourceTypeParam";

export const Biostorm = "基于用户输入信息，进行任务规划，不要做任何工具调用，不要输出任何明细代码，只进行任务规划";

const Environment: string = "## Environment Details\n" +
    "*   Operating System: ${System}\n" +
    "*   Workspace Path: ${Workspace}\n";

const ContextNone: string = "## Context\n" +
    "*   Active Document: None\n";

const ContextOtherNone: string = "## Other Open Documents\n" +
    "*   None\n";

const Context: string = "## Context\n" +
    "*   Active Document: `${currentFile}`\n" +
    "*   Location: `${currentFilePath}`\n" +
    "*   Current Line: ${currentLine}\n";

const ContextOtherTitle: string = "## Other Open Documents\n";

const ContextOtherContent: string = "*   `${otherFile}`\n" +
    "    *   Location: `${otherFilePath}`\n";

const LanguageAndPlatform: string = "*   `${language}`\n" +
    "${buildTool}";

const DependencySet: string = "*   `${dependencySet}`\n";


export function areSetsEqual(set1: Set<any> | any[], set2: Set<any> | any[]): boolean {
    const s1 = set1 instanceof Set ? set1 : new Set(set1);
    const s2 = set2 instanceof Set ? set2 : new Set(set2);

    if (s1.size !== s2.size) return false;

    for (const item of s1) {
        if (!s2.has(item)) return false;
    }

    return true;
}

export function assembleEnv(system: string, workspace: string): string {
    return Environment.replace("${System}", system).replace("${Workspace}", workspace);
}

export function assembleLanguageAndPlatform(language: string, buildTool: string, dependencySet?: string[]): string {
    language = language || "";
    buildTool = buildTool ? "*   `" + buildTool + "`\n" : "";
    let content = LanguageAndPlatform.replace("${language}", language).replace("${buildTool}", buildTool);
    dependencySet?.forEach(item => {
        content += DependencySet.replace("${dependencySet}", item);
    });
    return content;
}

export async function assembleResource(userQuestion: string, resourceTypeParams: ResourceTypeParam[]): Promise<string> {
    // TODO:
    return "";
}

export function assembleSelectFile(currentSelectPath: string | null, currentLine: number): string {
    if (currentSelectPath) {
        return Context
            .replace("${currentFile}", getFileName(currentSelectPath))
            .replace("${currentFilePath}", currentSelectPath)
            .replace("${currentLine}", currentLine.toString());
    }
    return ContextNone;
}


export function assembleOpenFile(currentOpen: string[] | null): string {
    if (currentOpen && currentOpen.length > 0) {
        let content = ContextOtherTitle;
        currentOpen.forEach(filePath => {
            content += ContextOtherContent
                .replace("${otherFile}", getFileName(filePath))
                .replace("${otherFilePath}", filePath);
        });
        return content;
    }
    return ContextOtherNone;
}
/**
 * shezhi 文件名
 */
function getFileName(filePath: string): string {
    return path.basename(filePath);
}