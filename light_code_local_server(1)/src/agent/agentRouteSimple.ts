import * as fs from 'fs';
import path from 'path';
import { AGENT_CONVERSATION_LIST_GET_ERROR, AGENT_DELETE_ERROR, AGENT_DISPLAY_MESSAGE_DETAIL_GET_ERROR, AGENT_QUESTION_DETAIL_GET_ERROR, AGENT_TOKEN_COST_GET_ERROR, FILE_CACHE_NOT_FOUND, ILLEGAL_MESSAGE_TYPE } from '../consts/ErrorCode';
import { AGENT_DELETE, AGENT_DETAIL, CONVERSATION_LIST, DISPLAY_MESSAGE_DETAIL, DISPLAY_MESSAGE_DETAIL_BY_ID } from '../consts/Urls';
import { GlobalStore, IDE_CLIENT } from '../GlobalStore';
import { IdeClient } from '../model/IdeClient';
import { request } from '../request/request';
import { createMessage, GrpcMessage } from '../server/grpcMessage';
import { MessageType } from '../server/MessageType';
import { getTokenCost } from '../utils/agentManager';
import { logger } from '../utils/Logger';
import { AgentDisplayMessage, AgentModelMessage, AgentStorage } from './AgentStorage';
import { AgentToolOperationMessage } from './AgentToolOperationMessage';
import { EditFileToolCache } from './tool/EditFileToolCache';
interface AgentTokenCostMessage {
    agentId: string;
    model: string;
    wordlistName: string;
}
export function routeSimple(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void): void {
    if (grpcMsg.messageType === MessageType.ide_to_proc_tool_call_resp) {
        toolCallResp(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_get_agent_conv_list_req) {
        getAgentConvList(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_agent_display_message_detail_req) {
        getAegntDisplayMessageDetail(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_agent_question_detail_req) {
        getAegntQuestionDetail(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_agent_conv_delete_req) {
        deleteAegnt(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_agent_tool_operation_req) {
        toolOperation(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_agent_cancel_req) {
        cancelAgent(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_agent_get_file_origin_content_req) {
        getFileOriginContent(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_agent_get_file_origin_path_req) {
        getFileOriginPath(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_agent_refresh_file_origin_content_req) {
        refreshFileOriginContent(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_agent_refresh_diff_count_req) {
        refreshDiffCount(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_edit_diff_operator_req) {
        editFileOperator(grpcMsg, callback);
    } else if (grpcMsg.messageType == MessageType.ide_to_proc_agent_token_cost_req) {
        getAgentTokenCost(grpcMsg, callback);
    } else {
        logger.error(`收到未知agent处理消息: ${JSON.stringify(grpcMsg)}`);
        const errorMessage = createMessage(
            grpcMsg.taskId,
            grpcMsg.requestId,
            grpcMsg.moduleType,
            MessageType.proc_to_ide_error_illegal_message_type,
            "未知的消息类型：" + grpcMsg.messageType,
            ILLEGAL_MESSAGE_TYPE
        );
        callback(null, errorMessage);
    }
}

function toolCallResp(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void): void {
    const { taskId, requestId, sender } = grpcMsg;
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(sender) as IdeClient;
    const task = client.tasks.get(taskId);
    if (task) {
        task.messages.set(requestId, grpcMsg.content);
    }
    callback(null, createMessage(
        grpcMsg.taskId,
        grpcMsg.requestId,
        grpcMsg.moduleType,
        MessageType.ide_to_proc_tool_call_resp,
        "success"
    ));
}
function getAgentConvList(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { keyword } = JSON.parse(grpcMsg.content) as { keyword: string };
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(grpcMsg.sender) as IdeClient;
    const body = JSON.stringify({
        user_account: client.userAccount,
        query: keyword
    });
    request(client.baseUrl + CONVERSATION_LIST, body)
        .then((resp) => {
            callback(null, createMessage(
                grpcMsg.taskId,
                grpcMsg.requestId,
                grpcMsg.moduleType,
                MessageType.proc_to_ide_get_agent_conv_list_resp,
                resp
            ));
        })
        .catch((error) => {
            logger.error('获取Agent对话列表失败', error instanceof Error ? error : new Error(String(error)));
            callback(null, createMessage(
                grpcMsg.taskId,
                grpcMsg.requestId,
                grpcMsg.moduleType,
                MessageType.proc_to_ide_get_agent_conv_list_resp,
                error instanceof Error ? error.message : String(error),
                AGENT_CONVERSATION_LIST_GET_ERROR
            ));
        });
}

function getAegntDisplayMessageDetail(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { agentConvId } = JSON.parse(grpcMsg.content) as { agentConvId: string };
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(grpcMsg.sender) as IdeClient;
    const body = JSON.stringify({
        user_account: client.userAccount,
        agent_conv_id: agentConvId
    });
    request(client.baseUrl + DISPLAY_MESSAGE_DETAIL, body)
        .then((resp) => {
            callback(null, createMessage(
                grpcMsg.taskId,
                grpcMsg.requestId,
                grpcMsg.moduleType,
                MessageType.proc_to_ide_agent_display_message_detail_resp,
                resp
            ));
        })
        .catch((error) => {
            logger.error('获取Agent对话历史详情失败', error instanceof Error ? error : new Error(String(error)));
            callback(null, createMessage(
                grpcMsg.taskId,
                grpcMsg.requestId,
                grpcMsg.moduleType,
                MessageType.proc_to_ide_agent_display_message_detail_resp,
                error instanceof Error ? error.message : String(error),
                AGENT_DISPLAY_MESSAGE_DETAIL_GET_ERROR
            ));
        });
}
function getAegntQuestionDetail(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { agentConvId, displayMessageId } = JSON.parse(grpcMsg.content) as { agentConvId: string, displayMessageId: string };
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(grpcMsg.sender) as IdeClient;
    const agentStorage = client.agentStorge;
    if (agentStorage && agentStorage.getAgentConvId() === agentConvId) {
        const displayMessage = agentStorage.getDisplayMessages().find((item: AgentDisplayMessage) => item.display_message_id === displayMessageId);
        if (displayMessage) {
            let respContent = "";
            agentStorage.getModelMessages().forEach((item: AgentModelMessage) => {
                if (item.display_message_id == displayMessageId) {
                    const { content } = JSON.parse(item.content);
                    respContent += "# " + item.message_role + " :\n" + content + "\n" + "***\n";
                }
            });
            callback(null, createMessage(
                grpcMsg.taskId,
                grpcMsg.requestId,
                grpcMsg.moduleType,
                MessageType.proc_to_ide_agent_question_detail_resp,
                respContent
            ));
        }
    } else {
        const body = JSON.stringify({
            display_message_id: displayMessageId
        });
        request(client.baseUrl + DISPLAY_MESSAGE_DETAIL_BY_ID, body)
            .then((resp) => {
                const data = JSON.parse(resp);
                let respContent = "";
                data.model_messages.forEach((item: AgentModelMessage) => {
                    const { content } = JSON.parse(item.content);
                    respContent += "# " + item.message_role + " :\n" + content + "\n" + "***\n";
                });
                callback(null, createMessage(
                    grpcMsg.taskId,
                    grpcMsg.requestId,
                    grpcMsg.moduleType,
                    MessageType.proc_to_ide_agent_question_detail_resp,
                    respContent
                ));
            })
            .catch((error) => {
                logger.error('查看问题详情失败', error instanceof Error ? error : new Error(String(error)));
                callback(null, createMessage(
                    grpcMsg.taskId,
                    grpcMsg.requestId,
                    grpcMsg.moduleType,
                    MessageType.proc_to_ide_agent_question_detail_resp,
                    error instanceof Error ? error.message : String(error),
                    AGENT_QUESTION_DETAIL_GET_ERROR
                ));
            });
    }
}
function deleteAegnt(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { agentConvId } = JSON.parse(grpcMsg.content) as { agentConvId: string };
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(grpcMsg.sender) as IdeClient;
    const body = JSON.stringify({
        agent_conv_id: agentConvId
    });
    request(client.baseUrl + AGENT_DELETE, body)
        .then((resp) => {
            callback(null, createMessage(
                grpcMsg.taskId,
                grpcMsg.requestId,
                grpcMsg.moduleType,
                MessageType.proc_to_ide_agent_conv_delete_resp,
                resp));
        })
        .catch((error) => {
            logger.error('删除Agent对话历史失败', error instanceof Error ? error : new Error(String(error)));
            callback(null, createMessage(
                grpcMsg.taskId,
                grpcMsg.requestId,
                grpcMsg.moduleType,
                MessageType.proc_to_ide_agent_conv_delete_resp,
                error instanceof Error ? error.message : String(error),
                AGENT_DELETE_ERROR
            ));
        });
}

function getAegntDetail(agentConvId: string, client: IdeClient): Promise<AgentStorage | null> {
    const body = JSON.stringify({
        user_account: client.userAccount,
        agent_conv_id: agentConvId
    });
    return request(client.baseUrl + AGENT_DETAIL, body)
        .then((resp) => {
            const data = JSON.parse(resp);
            if (!data) return null;

            const agentStorage = new AgentStorage("", "", 0, [],
                data.agent_conv_id,
                data.user_account,
                data.agent_id, [],
                "",
                data.multimodal
            );

            if (data.display_messages) {
                agentStorage.setDisplayMessages(data.display_messages);
            }
            if (data.model_messages) {
                agentStorage.setModelMessages(data.model_messages);
            }

            return agentStorage;
        })
        .catch((error) => {
            logger.error('获取Agent对话历史大模型消息详情失败', error instanceof Error ? error : new Error(String(error)));
            return null;
        });
}

function toolOperation(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { taskId, sender } = grpcMsg;
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(sender) as IdeClient;
    const task = client.tasks.get(taskId);
    if (task) {
        const operatorMsg = JSON.parse(grpcMsg.content) as AgentToolOperationMessage;
        task.messages.set(operatorMsg.toolId, grpcMsg.content);
    }
    callback(null, createMessage(
        grpcMsg.taskId,
        grpcMsg.requestId,
        grpcMsg.moduleType,
        MessageType.proc_to_ide_agent_tool_operation_resp,
        "success"
    ));
}

function cancelAgent(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { sender } = grpcMsg;
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(sender) as IdeClient;
    const { agentConvId } = JSON.parse(grpcMsg.content) as { agentConvId: string };
    client.removeTask(agentConvId);
    client.agentStorge?.saveAgent(client.baseUrl);
    callback(null, createMessage(
        grpcMsg.taskId,
        grpcMsg.requestId,
        grpcMsg.moduleType,
        MessageType.proc_to_ide_agent_cancel_resp,
        "success"
    ));
}

/**
 * 获取单次会话中指定文件的原始内容
 * 用户触发对比操作时，是用项目中的文件内容和此处获取到的原始内容进行对比
 * 因为每次修改操作时，内容都已经写入到了项目文件中去了
 * @param grpcMsg 
 * @param callback 
 */
function getFileOriginContent(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { sender } = grpcMsg;
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(sender) as IdeClient;
    const { toolId, filePath } = JSON.parse(grpcMsg.content);
    try {
        const fileName = path.basename(filePath);
        let originContent: string;
        if (toolId) {
            // 有传toolid时，就根据toolid去找对应的缓存文件
            const cacheFilePath = path.join(client.getIdeDir(), client.agentStorge?.getAgentConvId(), toolId, fileName);
            const cache = JSON.parse(fs.readFileSync(cacheFilePath, "utf-8")) as EditFileToolCache;
            originContent = cache.originContent;
        } else {
            // 没传toolid时，则直接返回上一次操作之后的缓存
            const cache = client.agentStorge?.getOrigiFileContentMap().get(filePath);
            originContent = cache?.originFileCachePath ? fs.readFileSync(cache.originFileCachePath, 'utf-8') : "";
        }
        callback(null, createMessage(
            grpcMsg.taskId,
            grpcMsg.requestId,
            grpcMsg.moduleType,
            MessageType.proc_to_ide_agent_get_file_origin_content_resp,
            originContent
        ));
    } catch (error: any) {
        logger.error(`读取文件缓存失败，原因: ${error}`);
        const errorMessage = createMessage(
            grpcMsg.taskId,
            grpcMsg.requestId,
            grpcMsg.moduleType,
            MessageType.proc_to_ide_agent_get_file_origin_content_resp,
            "读取文件缓存失败",
            FILE_CACHE_NOT_FOUND
        );
        callback(null, errorMessage);
    }
}

/**
 * 获取本地缓存的原始文件的路径
 */
function getFileOriginPath(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { sender } = grpcMsg;
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(sender) as IdeClient;
    const { filePath } = JSON.parse(grpcMsg.content);
    try {
        const cache = client.agentStorge?.getOrigiFileContentMap().get(filePath);
        callback(null, createMessage(
            grpcMsg.taskId,
            grpcMsg.requestId,
            grpcMsg.moduleType,
            MessageType.proc_to_ide_agent_get_file_origin_path_resp,
            cache?.originFileCachePath || ""
        ));
    } catch (error: any) {
        logger.error(`读取文件缓存失败，原因: ${error}`);
        const errorMessage = createMessage(
            grpcMsg.taskId,
            grpcMsg.requestId,
            grpcMsg.moduleType,
            MessageType.proc_to_ide_agent_get_file_origin_path_resp,
            "读取文件缓存失败",
            FILE_CACHE_NOT_FOUND
        );
        callback(null, errorMessage);
    }
}
/**
 * 刷新单次会话中指定文件的diff数量
 * 用户在对比操作时的每次操作，都要来刷新剩余的diff的数量
 * @param grpcMsg 
 * @param callback 
 */
function refreshDiffCount(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { sender } = grpcMsg;
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(sender) as IdeClient;
    const { filePath, diffCount, type } = JSON.parse(grpcMsg.content);
    const cache = client.agentStorge?.getOrigiFileContentMap().get(filePath);
    if (cache) {
        if (type === 'add') {
            cache.diffCount += diffCount;
        } else if (type === 'minus') {
            cache.diffCount -= diffCount;
        } else {
            cache.diffCount = diffCount;
        }
        client.agentStorge?.getOrigiFileContentMap().set(filePath, cache);
    }
    callback(null, createMessage(
        grpcMsg.taskId,
        grpcMsg.requestId,
        grpcMsg.moduleType,
        MessageType.proc_to_ide_agent_refresh_diff_count_resp,
        "success"
    ));
}

/**
 * 刷新单次会话中指定文件的原始内容
 * 用户在对比操作时的每次操作，都要来刷新原始内容的缓存
 * @param grpcMsg 
 * @param callback 
 */
function refreshFileOriginContent(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { sender } = grpcMsg;
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(sender) as IdeClient;
    const { filePath, originContent } = JSON.parse(grpcMsg.content);
    // 用户每次操作都会刷新原始文件缓存
    client.agentStorge?.getOrigiFileContentMap().set(filePath, originContent);
    callback(null, createMessage(
        grpcMsg.taskId,
        grpcMsg.requestId,
        grpcMsg.moduleType,
        MessageType.proc_to_ide_agent_refresh_file_origin_content_resp,
        "success"
    ));
}

/**
 * 编辑文件操作(当前文件的变更全部接受或者拒绝)
 * @param grpcMsg 
 * @param callback 
 */
function editFileOperator(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { sender } = grpcMsg;
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(sender) as IdeClient;
    const { filePath, operator } = JSON.parse(grpcMsg.content);
    if (operator == "accept") {
        const cache = client.agentStorge?.getOrigiFileContentMap().get(filePath);
        if (cache) {
            // 如果是全部接受操作，则修改原始文件内容为当前内容，并刷新diff数量为0
            cache.diffCount = 0;
            // 刷新缓存的原始文件内容为当前项目实际文件内容
            fs.writeFileSync(cache.originFileCachePath, fs.readFileSync(filePath, 'utf-8'));
            client.agentStorge?.getOrigiFileContentMap().set(filePath, cache);
        }
    } else if (operator == "refuse") {
        const cache = client.agentStorge?.getOrigiFileContentMap().get(filePath);
        if (cache) {
            // 如果是拒绝操作，则回退原始文件内容，并刷新diff数量为0
            // 刷新当前项目的实际文件内容为缓存的原始文件内容
            fs.writeFileSync(filePath, fs.readFileSync(cache.originFileCachePath, 'utf-8'));
            cache.diffCount = 0;
            client.agentStorge?.getOrigiFileContentMap().set(filePath, cache);
        }
    }

    callback(null, createMessage(
        grpcMsg.taskId,
        grpcMsg.requestId,
        grpcMsg.moduleType,
        MessageType.proc_to_ide_edit_diff_operator_resp,
        "success"
    ));
}
function getAgentTokenCost(grpcMsg: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { agentId, model, wordlistName } = JSON.parse(grpcMsg.content) as AgentTokenCostMessage;
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(grpcMsg.sender) as IdeClient;
    getTokenCost(agentId, model, wordlistName, client).then((tokenCost) => {
        callback(null, createMessage(
            grpcMsg.taskId,
            grpcMsg.requestId,
            grpcMsg.moduleType,
            MessageType.proc_to_ide_agent_token_cost_resp,
            JSON.stringify({
                tokenCost: tokenCost
            })
        ));
    }).catch((error) => {
        logger.error(`获取Agent所需tokens数量失败，原因: ${error}`);
        callback(null, createMessage(
            grpcMsg.taskId,
            grpcMsg.requestId,
            grpcMsg.moduleType,
            MessageType.proc_to_ide_agent_token_cost_resp,
            "当前模型：" + model + "，缺失词库配置，无法计算Agent所需tokens数量",
            AGENT_TOKEN_COST_GET_ERROR
        ));
    });
}

