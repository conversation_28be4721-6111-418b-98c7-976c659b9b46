import { ILLEGAL_MESSAGE_TYPE } from '../consts/ErrorCode';
import { AGENT_DETAIL } from '../consts/Urls';
import { GlobalStore, IDE_CLIENT } from '../GlobalStore';
import { IdeClient } from '../model/IdeClient';
import { Tool, ToolCall } from '../model/LLModel';
import { agentStreamRequest, request } from '../request/request';
import { ResourceTypeParam } from '../resource/ResourceTypeParam';
import { createMessage, GrpcMessage } from '../server/grpcMessage';
import { MessageType } from '../server/MessageType';
import { ModuleType } from '../server/ModuleType';
import { getAgentSystemPrompt, getAgentToolDefine, getAgentTools } from '../utils/agentManager';
import { logger } from '../utils/Logger';
import { tokenTools } from '../utils/toolTransUtil';
import { formatDateTime, generateUUID } from '../utils/util';
import { areSetsEqual, assembleEnv, assembleLanguageAndPlatform, assembleOpenFile, assembleSelectFile, Biostorm } from './agentPrompt';
import { AgentDisplayContent, AgentStorage, CodeInfo } from './AgentStorage';
import { AgentToolDefine } from './AgentToolDefine';
import Parser from './parser/AgentModelResponseParser';
import { ParserMessageType } from './parser/ParserMessageType';
import { ResponseMessageType } from './ResponseMessageType';
import { ToolCallResult } from './tool/ToolCallResult';
import { toolExecute } from './tool/toolExecute';

export interface AgentMessage {
    userQuestion: string;
    agentId: string;
    biostorm: boolean;
    resourceTypeParams: ResourceTypeParam[];
    resource: string;
    agentConvId: string;
    displayMessageId: string;
    currentSelect: string;
    currentLine: number;
    currentOpen: string[];
    system: string;
    workspace: string;
    language: string;
    buildTool: string;
    dependencySet: string[];
    userAccount: string;
    extraSystemPrompts: string[];
    promptType: string;
    msg: ChatMessage;
    model: string;
    wordlistName: string;
    imageAlgoType: string;
    requestTokenLimit: number;
    terminal: string;
    version: string;
    mac: string;
}

interface ChatMessage {
    chat_id: string;
    conv_id: string;
    type: string;
    terminal: string;
    terminal_version: string;
    source: string;
    prefix_text: string;
    suffix_text: string;
    display_question: string;
    model_params: string;
    extra: any;
    final_question: boolean;
}

class AgentResponse {
    displayMessageId: string;
    promptType: string;
    type: string;
    toolId?: string;
    autoExecute?: boolean;
    content: any;
    constructor(displayMessageId: string, promptType: string, type: string, content: any, toolId?: string, autoExecute?: boolean) {
        this.displayMessageId = displayMessageId;
        this.promptType = promptType;
        this.type = type;
        this.content = content;
        this.toolId = toolId;
        this.autoExecute = autoExecute;
    }
}

export function routeStream(grpcMsg: GrpcMessage, call: any) {
    const { taskId, requestId, sender } = grpcMsg;
    if (grpcMsg.messageType === MessageType.ide_to_proc_agent_stream_req) {
        const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(sender) as IdeClient;
        // 删除上一次的agent任务
        client.removeTask(client.agentStorge?.getTaskId());
        // 保存上一次的会话
        client.agentStorge?.saveAgent(client.baseUrl);
        // 缓存新任务
        client.addTask(taskId);
        client.addMessage(taskId, requestId, grpcMsg.content);
        const agentMessage = JSON.parse(grpcMsg.content) as AgentMessage;
        // 本次回答时，ai的displayMessageId，预先生成，后续报错需要
        const ansDisplayMessageId = generateUUID();
        execute(grpcMsg.taskId, grpcMsg.requestId, agentMessage, ansDisplayMessageId, client, call)
            .then((result) => {
                if (result.completed == "success") {
                    // 删除任务缓存
                    client.removeTask(grpcMsg.taskId);
                    // 执行结束，异步保存会话
                    client.agentStorge?.saveAgent(client.baseUrl);

                    // 发送结束消息
                    const stopMessage = createMessage(
                        grpcMsg.taskId,
                        grpcMsg.requestId,
                        grpcMsg.moduleType,
                        MessageType.proc_to_ide_agent_stream_resp,
                        JSON.stringify(new AgentResponse(ansDisplayMessageId, agentMessage.promptType, ResponseMessageType.stop, "[DONE]"))
                    );
                    call.write(stopMessage);
                    call.end();
                } else if (result.completed == "canceled") {
                    logger.info(`Agent执行取消: ${result.error}`);
                    // 删除任务缓存
                    client.removeTask(grpcMsg.taskId);
                    // 执行取消，异步保存会话
                    client.agentStorge?.saveAgent(client.baseUrl);
                    // 直接关闭连接，因为是取消，所以不需要再发送任何消息
                    call.end();
                } else if (result.completed == "error") {
                    logger.error(`Agent执行失败: ${result.error}`);
                    // 删除任务缓存
                    client.removeTask(grpcMsg.taskId);
                    // 执行结束，异步保存会话
                    client.agentStorge?.saveAgent(client.baseUrl);

                    // 发送异常消息
                    const cancelMessage = createMessage(
                        grpcMsg.taskId,
                        grpcMsg.requestId,
                        grpcMsg.moduleType,
                        MessageType.proc_to_ide_agent_stream_resp,
                        JSON.stringify(new AgentResponse(ansDisplayMessageId, agentMessage.promptType, ResponseMessageType.error, result.error))
                    );
                    call.write(cancelMessage);
                    call.end();
                }
            })
            .catch((error) => {
                logger.error('调用失败', error instanceof Error ? error : new Error(String(error)));
                // 删除任务缓存
                client.removeTask(grpcMsg.taskId);
                // 执行结束，异步保存会话
                client.agentStorge?.saveAgent(client.baseUrl);

                // 发送异常消息
                const errorMessage = createMessage(
                    grpcMsg.taskId,
                    grpcMsg.requestId,
                    grpcMsg.moduleType,
                    MessageType.proc_to_ide_agent_stream_resp,
                    JSON.stringify(new AgentResponse(ansDisplayMessageId, agentMessage.promptType, "error", error instanceof Error ? error.message : String(error)))
                );
                call.write(errorMessage);
                call.end();
            });
    } else {
        logger.error(`收到未知Agent消息: ${JSON.stringify(grpcMsg)}`);
        const errorMessage = createMessage(
            grpcMsg.taskId,
            grpcMsg.requestId,
            grpcMsg.moduleType,
            MessageType.proc_to_ide_error_illegal_message_type,
            "未知的消息类型：" + grpcMsg.messageType,
            ILLEGAL_MESSAGE_TYPE
        );
        call.write(errorMessage);
        call.end();
    }
}

async function execute(taskId: string,
    requestId: string,
    message: AgentMessage,
    ansDisplayMessageId: string,
    client: IdeClient,
    call: any): Promise<{ completed: string, error: string }> {
    if (!message.model) {
        return { completed: "error", error: "没有模型权限" };
    }
    // 如果promptType不为空，则认为是chat类型
    if (message.promptType) {
        return await executeChat(taskId, requestId, message, ansDisplayMessageId, client, call);
    } else {
        return await executeAgent(taskId, requestId, message, ansDisplayMessageId, client, call);
    }
}

async function nextExecute(agentStorage: AgentStorage,
    displayMessageId: string,
    taskId: string,
    requestId: string,
    agentMessage: AgentMessage,
    toolsDes: any,
    biostorm: boolean,
    client: IdeClient,
    call: any): Promise<{ completed: string, error: string }> {
    if (client.isCanceled(taskId)) {
        return { completed: "canceled", error: "任务已经取消" };
    }
    if (agentMessage.requestTokenLimit <= 0) {
        return { completed: "error", error: "上下文长度超过限制" };
    }
    const parser = new Parser();
    const finalMessages = await agentStorage.getFinalCompletion(agentMessage, biostorm, toolsDes);
    if (finalMessages.completion?.messages?.length == 0 || finalMessages.multimodal_completion?.messages?.length == 0) {
        return { completed: "error", error: "上下文长度超过限制" };
    }
    return new Promise((resolve) => {
        agentStreamRequest(
            finalMessages,
            parser,
            client.baseUrl,
            async (message) => {
                try {
                    if (message.type == ParserMessageType.stop) {
                        if (biostorm) {
                            agentStorage.removeMessage(agentStorage.getModelMessages().length - 1);
                            agentStorage.appendMessage({ role: parser.getMessage().role, content: parser.getMessage().content }, displayMessageId);
                            agentStorage.appendMessage({ role: "system", content: "按照以上任务规划进行执行" }, displayMessageId, 'biostorm');

                            // 保存display message
                            const chatContents: any[] = [];
                            // 如果模型有思考过程，则将思考过程加入到回答前
                            if (parser.getMessage().reasoning_content) {
                                chatContents.push({ type: ResponseMessageType.reasoningTxt, content: parser.getMessage().reasoning_content });
                            }
                            chatContents.push({ type: ResponseMessageType.txt, content: parser.getMessage().content });

                            agentStorage.appendChat({
                                display_message_id: displayMessageId,
                                chat_role: "AI",
                                content: chatContents,
                                model_name: agentMessage.model,
                                terminal: agentMessage.terminal,
                                terminal_version: agentMessage.version,
                            });
                            const result = await nextExecute(agentStorage, displayMessageId, taskId, requestId, agentMessage, toolsDes, false, client, call);
                            resolve(result);
                        } else {
                            agentStorage.appendMessage({ role: parser.getMessage().role, content: parser.getMessage().content }, displayMessageId);

                            // 保存display message
                            const chatContents: any[] = [];
                            // 如果模型有思考过程，则将思考过程加入到回答前
                            if (parser.getMessage().reasoning_content) {
                                chatContents.push({ type: ResponseMessageType.reasoningTxt, content: parser.getMessage().reasoning_content });
                            }
                            chatContents.push({ type: ResponseMessageType.txt, content: parser.getMessage().content });

                            agentStorage.appendChat({
                                display_message_id: displayMessageId,
                                chat_role: "AI",
                                content: chatContents,
                                model_name: agentMessage.model,
                                terminal: agentMessage.terminal,
                                terminal_version: agentMessage.version,
                            });
                            resolve({ completed: "success", error: "" });
                        }
                    } else if (message.type == ParserMessageType.continueCall) {
                        // 保存tool call前的message（如果存在）
                        const chatContents: any[] = [];
                        // 如果模型有思考过程，则将思考过程加入到回答前
                        if (parser.getMessage().reasoning_content) {
                            chatContents.push({ type: ResponseMessageType.reasoningTxt, content: parser.getMessage().reasoning_content });
                            // 清空 reasoning_content（否则多次tool call会导致内容叠加存）
                            parser.getMessage().reasoning_content = "";
                        }
                        if (parser.getMessage().content) {
                            chatContents.push({ type: ResponseMessageType.txt, content: parser.getMessage().content });
                            // 清空 content（否则多次tool call会导致内容叠加存）
                            parser.getMessage().content = "";
                        }

                        if (chatContents.length > 0) {
                            agentStorage.appendChat({
                                display_message_id: displayMessageId,
                                chat_role: "AI",
                                content: chatContents,
                                model_name: agentMessage.model,
                                terminal: agentMessage.terminal,
                                terminal_version: agentMessage.version,
                            });
                        }

                        agentStorage.appendMessage(parser.getMessage(), displayMessageId);
                        parser.getToolCallResults().forEach(item => {
                            agentStorage.appendMessage({ role: "tool", content: JSON.stringify(item), "tool_call_id": item.toolCallId }, displayMessageId);
                        });
                        const result = await nextExecute(agentStorage, displayMessageId, taskId, requestId, agentMessage, toolsDes, false, client, call);
                        resolve(result);
                    } else if (message.type == ParserMessageType.txt) {
                        const txtMessage = createMessage(
                            taskId,
                            requestId,
                            ModuleType.agent,
                            MessageType.proc_to_ide_agent_stream_resp,
                            JSON.stringify(new AgentResponse(displayMessageId, agentMessage.promptType, ResponseMessageType.txt, message.content))
                        );
                        if (!client.isCanceled(taskId)) {
                            call.write(txtMessage);
                        }
                    } else if (message.type == ParserMessageType.reasoningTxt) {
                        const txtMessage = createMessage(
                            taskId,
                            requestId,
                            ModuleType.agent,
                            MessageType.proc_to_ide_agent_stream_resp,
                            JSON.stringify(new AgentResponse(displayMessageId, agentMessage.promptType, ResponseMessageType.reasoningTxt, message.content))
                        );
                        if (!client.isCanceled(taskId)) {
                            call.write(txtMessage);
                        }
                    } else if (message.type == ParserMessageType.toolCall) {
                        const toolCall = message.content as ToolCall;
                        const agentToolDefine = getAgentToolDefine(agentMessage.agentId, toolCall.function.name, client);
                        const startTime = formatDateTime();
                        toolStart(displayMessageId, agentMessage, toolCall, taskId, requestId, call, client, agentToolDefine);
                        let toolCallResult: ToolCallResult;
                        if (agentToolDefine) {
                            toolCallResult = await toolExecute(
                                taskId,
                                toolCall,
                                agentToolDefine,
                                call,
                                agentStorage.getAgentConvId(),
                                displayMessageId,
                                client);
                        } else {
                            toolCallResult = new ToolCallResult(
                                toolCall.function.name,
                                JSON.stringify(toolCall.function.arguments),
                                false,
                                `工具类不存在:${toolCall.function.name}`,
                                toolCall.id
                            );
                        }
                        parser.getToolCallResults().push(toolCallResult);
                        toolEnd(displayMessageId, agentMessage, toolCall.id, toolCallResult, taskId, requestId, call, client);

                        // 保存tool call前的message（如果存在）
                        const chatContents: any[] = [];
                        // 如果模型有思考过程，则将思考过程加入到回答前
                        if (parser.getMessage().reasoning_content) {
                            chatContents.push({ type: ResponseMessageType.reasoningTxt, content: parser.getMessage().reasoning_content });
                            // 清空 reasoning_content（否则多次tool call会导致内容叠加存）
                            parser.getMessage().reasoning_content = "";
                        }
                        if (parser.getMessage().content) {
                            chatContents.push({ type: ResponseMessageType.txt, content: parser.getMessage().content });
                            // 清空 content（否则多次tool call会导致内容叠加存）
                            parser.getMessage().content = "";
                        }

                        if (chatContents.length > 0) {
                            agentStorage.appendChat({
                                display_message_id: displayMessageId,
                                chat_role: "AI",
                                content: chatContents,
                                model_name: agentMessage.model,
                                terminal: agentMessage.terminal,
                                terminal_version: agentMessage.version,
                            });
                        }
                        // 消息后写。考虑到中间执行可能中断等异常情况。为了确保写入的消息都是成对的，所以这里统一在执行结束之后写入
                        // 避免出现只有start没有end的情况。因为大模型要求toolId必须是成对的
                        const startContent = new AgentResponse(
                            displayMessageId,
                            agentMessage.promptType,
                            ResponseMessageType.tool_start,
                            toolCall,
                            toolCall.id,
                            agentToolDefine?.autoExec
                        );
                        agentStorage.appendChat({
                            display_message_id: displayMessageId,
                            chat_role: "AI",
                            content: [startContent],
                            request_time: startTime,
                            model_name: agentMessage.model,
                            terminal: agentMessage.terminal,
                            terminal_version: agentMessage.version,
                        });
                        const endContent = new AgentResponse(
                            displayMessageId,
                            agentMessage.promptType,
                            ResponseMessageType.tool_end,
                            toolCallResult,
                            toolCall.id,
                        );
                        agentStorage.appendChat({
                            display_message_id: displayMessageId,
                            chat_role: "AI",
                            content: [endContent],
                            model_name: agentMessage.model,
                            terminal: agentMessage.terminal,
                            terminal_version: agentMessage.version,
                        });
                    }
                } catch (error) {
                    agentStorage.appendChat({
                        display_message_id: displayMessageId,
                        chat_role: "AI",
                        content: [{ type: ResponseMessageType.error, content: error instanceof Error ? error.message : String(error) }],
                        model_name: agentMessage.model,
                        terminal: agentMessage.terminal,
                        terminal_version: agentMessage.version,
                    });
                    resolve({ completed: "error", error: error instanceof Error ? error.message : String(error) });
                }
            }
        ).catch(error => {
            agentStorage.appendChat({
                display_message_id: displayMessageId,
                chat_role: "AI",
                content: [{ type: ResponseMessageType.error, content: error instanceof Error ? error.message : String(error) }],
                model_name: agentMessage.model,
                terminal: agentMessage.terminal,
                terminal_version: agentMessage.version,
            });
            resolve({ completed: "error", error: error instanceof Error ? error.message : String(error) });
        });
    });
}

function toolEnd(displayMessageId: string, agentMessage: AgentMessage, toolCallId: string, toolCallResult: ToolCallResult, taskId: string, requestId: string, call: any, client: IdeClient) {
    if (client.isCanceled(taskId)) {
        return;
    }
    const endContent = new AgentResponse(
        displayMessageId,
        agentMessage.promptType,
        ResponseMessageType.tool_end,
        toolCallResult,
        toolCallId,
    );

    const endMessage = createMessage(
        taskId,
        requestId,
        ModuleType.agent,
        MessageType.proc_to_ide_agent_stream_resp,
        JSON.stringify(endContent)
    );
    call.write(endMessage);
}

function toolStart(displayMessageId: string, agentMessage: AgentMessage, toolCall: ToolCall, taskId: string, requestId: string, call: any, client: IdeClient, agentToolDefine?: AgentToolDefine) {
    if (client.isCanceled(taskId)) {
        return;
    }
    const startContent = new AgentResponse(
        displayMessageId,
        agentMessage.promptType,
        ResponseMessageType.tool_start,
        toolCall,
        toolCall.id,
        agentToolDefine?.autoExec
    );
    const beginMessage = createMessage(
        taskId,
        requestId,
        ModuleType.agent,
        MessageType.proc_to_ide_agent_stream_resp,
        JSON.stringify(startContent)
    );
    call.write(beginMessage);
}

function getAegntDetail(agentConvId: string, client: IdeClient): Promise<AgentStorage | null> {
    const body = JSON.stringify({
        user_account: client.userAccount,
        agent_conv_id: agentConvId
    });
    return request(client.baseUrl + AGENT_DETAIL, body)
        .then((resp) => {
            const data = JSON.parse(resp);
            if (!data) return null;

            const agentStorage = new AgentStorage("", "", 0, [],
                data.agent_conv_id,
                data.user_account,
                data.agent_id, [],
                "",
                data.multimodal
            );

            if (data.display_messages) {
                agentStorage.setDisplayMessages(data.display_messages);
            }
            if (data.model_messages) {
                agentStorage.setModelMessages(data.model_messages);
            }

            return agentStorage;
        })
        .catch((error) => {
            logger.error('获取Agent对话历史大模型消息详情失败', error instanceof Error ? error : new Error(String(error)));
            return null;
        });
}

async function executeAgent(taskId: string, requestId: string, message: AgentMessage, ansDisplayMessageId: string, client: IdeClient, call: any):
    Promise<{ completed: string; error: string; }> {
    const userDisplayMessageId = message.displayMessageId;
    let agentStorge = client.agentStorge;
    // 不存在上一次会话，或者这是一个新的会话
    if (agentStorge == null || agentStorge.getAgentConvId() != message.agentConvId) {
        // 创建新的会话缓存
        const currentOpen = message.currentOpen || [];
        agentStorge = new AgentStorage(
            taskId,
            message.currentSelect || '',
            message.currentLine || 0,
            currentOpen,
            message.agentConvId,
            message.userAccount,
            message.agentId,
            message.extraSystemPrompts || [],
            message.promptType,
            false
        );
        // 查询会话历史
        const agentHistory = await getAegntDetail(message.agentConvId, client);
        if (agentHistory && agentHistory != null) {
            agentHistory?.getDisplayMessages()?.forEach(item => {
                item.sent = true;
            });
            agentHistory?.getModelMessages()?.forEach(item => {
                item.sent = true;
            });
            agentStorge.setDisplayMessages(agentHistory.getDisplayMessages());
            agentStorge.setModelMessages(agentHistory.getModelMessages());
        }

        client.agentStorge = agentStorge;
    } else {
        agentStorge.setTaskId(taskId);
        agentStorge.setType(message.promptType);
    }
    const systemPrompt = getAgentSystemPrompt(message.agentId, client);
    if (agentStorge.getModelMessages().length == 0) {
        if (systemPrompt) {
            agentStorge.appendMessage({ role: 'system', content: systemPrompt }, userDisplayMessageId);
        }
        if (message.system && message.workspace) {
            agentStorge.appendMessage({ role: 'system', content: assembleEnv(message.system, message.workspace) }, userDisplayMessageId);
        }
        if (agentStorge.getCurrentSelect()) {
            agentStorge.appendMessage({ role: 'system', content: assembleSelectFile(agentStorge.getCurrentSelect(), agentStorge.getCurrentLine()) }, userDisplayMessageId, 'current_select');
        }
        if (agentStorge.getCurrentOpen()) {
            agentStorge.appendMessage({ role: 'system', content: assembleOpenFile(agentStorge.getCurrentOpen()) }, userDisplayMessageId, 'current_open');
        }
        if (message.language || message.buildTool || message.dependencySet) {
            agentStorge.appendMessage({ role: 'system', content: assembleLanguageAndPlatform(message.language, message.buildTool, message.dependencySet) }, userDisplayMessageId);
        }
        if (message.extraSystemPrompts && message.extraSystemPrompts.length > 0) {
            message.extraSystemPrompts.forEach(prompt => {
                agentStorge.appendMessage({ role: 'system', content: prompt }, userDisplayMessageId, 'extra_system_prompts');
            })
        }
    } else {
        if (agentStorge.getCurrentSelect() != message.currentSelect && (message.currentSelect || message.currentLine)) {
            agentStorge.setCurrentSelect(message.currentSelect);
            agentStorge.appendMessage({ role: 'system', content: assembleSelectFile(message.currentSelect, message.currentLine) }, userDisplayMessageId, 'current_select');
        }
        if (!areSetsEqual(agentStorge.getCurrentOpen(), message.currentOpen) && message.currentOpen) {
            agentStorge.setCurrentOpen(message.currentOpen);
            agentStorge.appendMessage({ role: 'system', content: assembleOpenFile(message.currentOpen) }, userDisplayMessageId, 'current_open');
        }

        if (!areSetsEqual(agentStorge.getExtraSystemPrompts(), message.extraSystemPrompts)
            && message.extraSystemPrompts
            && message.extraSystemPrompts.length > 0) {
            agentStorge.setExtraSystemPrompts(message.extraSystemPrompts);
            message.extraSystemPrompts.forEach(prompt => {
                agentStorge.appendMessage({ role: 'system', content: prompt }, userDisplayMessageId, 'extra_system_prompts');
            })
        }
    }
    if (message.resource) {
        agentStorge.appendMessage({ role: 'system', content: message.resource }, userDisplayMessageId, 'resource');
    }
    // if (message.resourceTypeParams && message.resourceTypeParams.length > 0) {
    //     agentStorage.appendMessage({ role: "system", content: assembleResource(message.userQuestion, message.resourceTypeParams) }, displayMessageId);
    // }
    const userchat: AgentDisplayContent[] = [];
    userchat.push({ type: ResponseMessageType.txt, content: message.userQuestion });
    if (message.resourceTypeParams && message.resourceTypeParams.length > 0) {
        message.resourceTypeParams.forEach(item => {
            if (item.typeName === 'image') {
                agentStorge.appendMessage({ role: 'user', content: item.content }, userDisplayMessageId, 'image_url');
                agentStorge.setMultimodal(true);
                userchat.push({ type: ResponseMessageType.image_url, content: item.content });
            }
        })
    }
    agentStorge.appendMessage({ role: "user", content: message.userQuestion }, userDisplayMessageId);

    agentStorge.appendChat({
        display_message_id: userDisplayMessageId,
        chat_role: "user",
        content: userchat,
        model_name: message.model,
        terminal: message.terminal,
        terminal_version: message.version,
    });
    const toolsDes: Tool[] = getAgentTools(message.agentId, client);
    // 计算工具token消耗
    const toolTokenCost = await tokenTools(toolsDes, message.model, message.wordlistName);
    message.requestTokenLimit -= toolTokenCost;
    if (message.biostorm) {
        agentStorge.appendMessage({ role: "system", content: Biostorm }, userDisplayMessageId);
        return await nextExecute(agentStorge, ansDisplayMessageId, taskId, requestId, message, toolsDes, true, client, call);
    } else {
        return await nextExecute(agentStorge, ansDisplayMessageId, taskId, requestId, message, toolsDes, false, client, call);
    }
}

async function executeChat(taskId: string, requestId: string, message: AgentMessage, ansDisplayMessageId: string, client: IdeClient, call: any):
    Promise<{ completed: string; error: string; }> {
    const userDisplayMessageId = message.displayMessageId;
    let agentStorge = client.agentStorge;
    const chatMessage = message.msg;
    if (agentStorge == null || agentStorge.getAgentConvId() != message.agentConvId) {
        // 创建新的会话缓存
        agentStorge = new AgentStorage(
            taskId,
            '',
            0,
            [],
            message.agentConvId,
            message.userAccount,
            '',
            message.extraSystemPrompts || [],
            message.promptType,
            chatMessage.extra?.CHAT_RESOURCES?.length > 0 || false
        );
        // 查询会话历史
        const agentHistory = await getAegntDetail(message.agentConvId, client);
        if (agentHistory && agentHistory != null) {
            agentHistory?.getDisplayMessages()?.forEach(item => {
                item.sent = true;
            });
            agentHistory?.getModelMessages()?.forEach(item => {
                item.sent = true;
            });
            agentStorge.setDisplayMessages(agentHistory.getDisplayMessages());
            agentStorge.setModelMessages(agentHistory.getModelMessages());
            agentStorge.setMultimodal(agentHistory.getMultimodal());
        }
        client.agentStorge = agentStorge;
    } else {
        agentStorge.setTaskId(taskId);
        agentStorge.setType(message.promptType);
    }

    if (message.agentId && message.agentId.length > 0) {
        if (agentStorge.getModelMessages().length == 0) {
            const systemPrompt = getAgentSystemPrompt(message.agentId, client);
            if (systemPrompt) {
                agentStorge.appendMessage({ role: 'system', content: systemPrompt }, userDisplayMessageId);
            }
        }
    }

    const userchat: AgentDisplayContent[] = [];
    userchat.push({ type: ResponseMessageType.txt, content: chatMessage.display_question });
    if (chatMessage.extra) {
        const codeInfo = chatMessage.extra.代码相关信息 as CodeInfo;
        if (codeInfo) {
            codeInfo.display_message_id = userDisplayMessageId;
            agentStorge.addCodeInfo(codeInfo);
        }
        const images = chatMessage.extra.CHAT_RESOURCES;
        if (images && images.length > 0) {
            images.forEach((value: any) => {
                agentStorge.appendMessage({ role: "user", content: value.resource_content }, userDisplayMessageId, value.resource_type);
                userchat.push({ type: ResponseMessageType.image_url, content: value.resource_content });
            })
            agentStorge.setMultimodal(true);
        }
    }
    agentStorge.appendMessage({ role: "user", content: chatMessage.prefix_text }, userDisplayMessageId);
    agentStorge.appendChat({
        display_message_id: userDisplayMessageId,
        chat_role: "user",
        content: userchat,
        model_name: message.model,
        terminal: message.terminal,
        terminal_version: message.version,
    });
    if (message.agentId && message.agentId.length > 0) {
        const toolsDes: Tool[] = getAgentTools(message.agentId, client);
        // 计算工具token消耗
        const toolTokenCost = await tokenTools(toolsDes, message.model, message.wordlistName);
        message.requestTokenLimit -= toolTokenCost;
        if (message.biostorm) {
            agentStorge.appendMessage({ role: "system", content: Biostorm }, userDisplayMessageId);
            return await nextExecute(agentStorge, ansDisplayMessageId, taskId, requestId, message, toolsDes, true, client, call);
        } else {
            return await nextExecute(agentStorge, ansDisplayMessageId, taskId, requestId, message, toolsDes, false, client, call);
        }
    } else {
        return await nextExecute(agentStorge, ansDisplayMessageId, taskId, requestId, message, null, false, client, call);
    }
}
