import { MessageResponse, ToolCall, Result } from '../../model/LLModel';
import { ParserMessageType } from './ParserMessageType';
import { ToolCallResult } from '../tool/ToolCallResult';

interface MessageEvent {
    type: string;
    content: string | ToolCall | '';
}

type MessageCallback = (event: MessageEvent) => Promise<void>;

class Parser {
    private message: MessageResponse;
    private existingToolCall: Partial<ToolCall>[];
    private toolCallResults: ToolCallResult[];

    constructor() {
        this.message = {
            role: '',
            content: '',
            reasoning_content: '',
            tool_calls: [],
            is_finished: false
        };
        this.existingToolCall = [];
        this.toolCallResults = [];
    }

    public getMessage(): MessageResponse {
        return this.message;
    }

    public getToolCallResults(): any[] {
        return this.toolCallResults;
    }

    async parserResult(result: Result, onMessage: MessageCallback): Promise<void> {
        const choices = result.choices;
        if (!choices || choices.length === 0) {
            return;
        }

        const choice = choices[0];
        if (choice.index > 0) {
            return;
        }

        const delta = choice.delta;
        if (delta?.role) {
            this.message.role = delta.role;
        }
        
        if (delta?.reasoning_content) {
            this.message.reasoning_content += delta.reasoning_content;
            await onMessage({
                type: ParserMessageType.reasoningTxt,
                content: delta.reasoning_content
            });
        }

        if (delta?.content) {
            this.message.content += delta.content;
            await onMessage({
                type: ParserMessageType.txt,
                content: delta.content
            });
        }

        if (delta?.tool_calls) {
            for (const toolCall of delta.tool_calls) {
                if (toolCall.id && this.existingToolCall.length > 0) {
                    await onMessage({
                        type: ParserMessageType.toolCall,
                        content: this.parserTool()
                    });
                    this.existingToolCall = [];
                }
                this.existingToolCall.push(toolCall);
            }
        }

        if (choice?.finish_reason) {
            if (this.existingToolCall.length > 0) {
                await onMessage({
                    type: ParserMessageType.toolCall,
                    content: this.parserTool()
                });
            }

            if (this.message.tool_calls && this.message.tool_calls.length > 0) {
                await onMessage({
                    type: ParserMessageType.continueCall,
                    content: ''
                });
            } else {
                if (this.getMessage().is_finished) {
                    return;
                }
                this.getMessage().is_finished = true;
                await onMessage({
                    type: ParserMessageType.stop,
                    content: ''
                });
            }
        }
    }

    reset(): void {
        this.message = {
            role: '',
            content: '',
            reasoning_content: '',
            tool_calls: [],
            is_finished: false
        };
    }

    private parserTool(): ToolCall {
        const tool: ToolCall = {
            id: "",
            type: "",
            index: 0,
            function: {
                name: "",
                arguments: ""
            }
        };

        this.existingToolCall.forEach(item => {
            if (item.id) {
                tool.id = item.id;
            }
            if (item.type) {
                tool.type = item.type;
            }
            if (item.index !== undefined) {
                tool.index = item.index;
            }
            if (item.function) {
                const func = item.function;
                if (func.name) {
                    tool.function.name = func.name;
                }
                if (func.arguments) {
                    tool.function.arguments += func.arguments;
                }
            }
        });

        this.message.tool_calls.push(tool);
        return tool;
    }
}

export default Parser; 