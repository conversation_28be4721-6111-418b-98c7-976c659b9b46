import { MessageRequest } from "../../model/LLModel";

/**
     * 默认截取策略
     * 保留system类型消息
     * 保留当前问之前的最近一次user问
     * 当前问倒推10条数据作为保留数据
     *
     * @param messages
     * @return
     */
export function defaultSummaryStrategy(messages: MessageRequest[]) {
    if (messages.length < 15) {
        return messages;
    }
    const result: MessageRequest[] = [];
    for (const message of messages) {
        // system类型消息保留
        if (message.role == "system") {
            result.push(message);
        } else {
            break;
        }
    }
    // 取当前数组最后10条数据，因为包含当前用户问，所以相当于取了9条历史数据。
    const remain: MessageRequest[] = messages.slice(messages.length - 10, messages.length);
    // 如果第一条数据类型是tool，则需要把其前一条大模型响应数据也加进来(即对应的toolcalls消息)，否则大模型会认为tool调用信息不完整而报错
    // 因为大模型返回的toolcalls可能是个数组，所以它后面会跟N个tool类型的消息体，这些消息体必须是完整的
    if (remain[0].role == "tool") {
        for (let i = messages.length - 11; i >= 0; i--) {
            remain.unshift(messages[i]);
            if (messages[i].role != "tool") {
                break;
            }
        }
    }
    // 如果9条历史数据中有user类型消息，则直接返回，否则继续往前取最近的一次用户问消息
    for (const message of remain) {
        if (message.role == "user") {
            result.push(...remain);
            return result;
        }
    }
    // 继续往前取最近的一次用户问消息,从倒数11条数据开始
    for (let i = messages.length - 11; i >= 0; i--) {
        if (messages[i].role == "user") {
            result.push(messages[i]);
            break;
        }
    }
    result.push(...remain);
    return result;
}