export class EditFileToolCache {
    agentConvId: string;
    displayMessageId: string;
    toolId: string;
    filePath: string;
    /**
     * 原始内容
     */
    originContent: string;
    /**
     * 大模型响应
     */
    codeEdit: string;
    constructor(agentConvId: string,
        displayMessageId: string,
        toolId: string,
        filePath: string,
        originContent: string,
        codeEdit: string) {
        this.agentConvId = agentConvId;
        this.displayMessageId = displayMessageId;
        this.toolId = toolId;
        this.filePath = filePath;
        this.originContent = originContent;
        this.codeEdit = codeEdit
    }
}