export class ToolCallResult {
    success: boolean;
    errorMessage: string;
    result: string;
    toolName: string;
    toolCallId: string;
    args: string;

    constructor(
        toolName: string = '',
        args: string = '',
        success: boolean = false,
        result: string = '',
        toolCallId: string = '',
        errorMessage: string = ''
    ) {
        this.success = success;
        this.errorMessage = errorMessage;
        this.result = result;
        this.toolName = toolName;
        this.toolCallId = toolCallId;
        this.args = args;
    }
}