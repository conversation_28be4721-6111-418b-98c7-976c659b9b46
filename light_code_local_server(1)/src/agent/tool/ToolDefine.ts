import { Tool } from "../../model/LLModel";

export class ToolDefine {
    name: string;
    source: string;
    tool: Tool;
    displayName?: string;
    constructor(
        name: string,
        source: string,
        tool: Tool,
        displayName?: string
    ) {
        this.name = name;
        this.source = source;
        this.tool = tool;
        this.displayName = displayName || name;
    }
}
