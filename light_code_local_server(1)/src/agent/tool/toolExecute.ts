import { grep_search, view_file, write_to_file, list_dir, codebase_search, related_files, edit_file, GrepSearchParam, ViewFileParam, WriteToFileParam } from "./tools";
import { createMessage } from "../../server/grpcMessage";
import { MessageType } from "../../server/MessageType";
import { ModuleType } from "../../server/ModuleType";
import { generateUUID } from "../../utils/util";
import { ToolCall } from "../../model/LLModel";
import { executeMcpToolWithCallback } from "../../mcp/mcp";
import { IdeClient } from "../../model/IdeClient";
import { waitForCancelResponse } from "../../utils/httpUtil";
import { ToolCallResult } from "./ToolCallResult";
import { AgentType } from "../AgentDefine";
import { AgentToolDefine } from "../AgentToolDefine";
import { AgentToolOperationMessage } from "../AgentToolOperationMessage";

export async function toolExecute(
    taskId: string,
    toolCall: ToolCall,
    agentToolDefine: AgentToolDefine,
    call: any,
    agentConvId: string,
    displayMessageId: string,
    client: IdeClient): Promise<ToolCallResult> {
    if (!agentToolDefine.autoExec) {
        while (true) {
            const { promise, cancel } = waitForCancelResponse(client, taskId, toolCall.id);
            client.agentStorge.addInnerTask(cancel);
            const response = await promise;
            client.agentStorge.removeInnerTask(cancel);
            const operatorMsg = JSON.parse(response) as AgentToolOperationMessage;
            if (operatorMsg.operator === 'accept') {
                return doExecute(agentToolDefine, taskId, toolCall, call, agentConvId, displayMessageId, client);
            } else if (operatorMsg.operator === 'cancel') {
                return new ToolCallResult(
                    toolCall.function.name,
                    JSON.stringify(toolCall.function.arguments),
                    false,
                    `用户取消调用工具类`,
                    toolCall.id
                );
            } else if (operatorMsg.operator === 'edit') {
                if (operatorMsg.args) {
                    const content = JSON.parse(operatorMsg.args);
                    toolCall.function.arguments = content.function.arguments;
                }
                continue;
            } else {
                return new ToolCallResult(
                    toolCall.function.name,
                    JSON.stringify(toolCall.function.arguments),
                    false,
                    `用户拒绝调用工具类`,
                    toolCall.id
                );
            }
        }
    } else {
        return doExecute(agentToolDefine, taskId, toolCall, call, agentConvId, displayMessageId, client);
    }
}

async function doExecute(
    agentToolDefine: AgentToolDefine,
    taskId: string,
    toolCall: ToolCall,
    call: any,
    agentConvId: string,
    displayMessageId: string,
    client: IdeClient): Promise<ToolCallResult> {
    if (agentToolDefine.source === AgentType.system) {
        return await localExecute(toolCall.function.name, toolCall.id, toolCall.function.arguments, agentConvId, displayMessageId, client);
    } else if (agentToolDefine.source === AgentType.custom) {
        // 工具调用的时候，要生成自己的单独的requestId
        const toolCallMessage = createMessage(
            taskId,
            generateUUID(),
            ModuleType.agent,
            MessageType.proc_to_ide_tool_call_req,
            JSON.stringify(toolCall)
        );
        call.write(toolCallMessage);
        // 等待客户端响应
        const { promise, cancel } = waitForCancelResponse(client, taskId, toolCallMessage.requestId);
        client.agentStorge.addInnerTask(cancel);
        const response = await promise;
        client.agentStorge.removeInnerTask(cancel);
        if (response) {
            const { toolCallResult } = JSON.parse(response);
            return toolCallResult;
        } else {
            return new ToolCallResult(
                toolCall.function.name,
                JSON.stringify(toolCall.function.arguments),
                false,
                `工具调用超时`,
                toolCall.id
            );
        }
    } else {
        // 如果本地没有，则从MCP获取
        const mcpConnection = client.mcpHub.connections.get(agentToolDefine.source);
        if (mcpConnection && mcpConnection.server.status === "connected") {
            const mcpToolName = client.mcpHub.toolToMcpTool.get(toolCall.function.name);
            if (mcpToolName) {
                return await executeMcpToolWithCallback(
                    mcpToolName,
                    toolCall.id,
                    toolCall.function.arguments,
                    mcpConnection
                );
            } else {
                return new ToolCallResult(
                    toolCall.function.name,
                    JSON.stringify(toolCall.function.arguments),
                    false,
                    `工具类所在的MCP服务器不存在或未连接或被禁用:${agentToolDefine.source}`,
                    toolCall.id
                );
            }
        } else {
            return new ToolCallResult(
                toolCall.function.name,
                JSON.stringify(toolCall.function.arguments),
                false,
                `工具类所在的MCP服务器不存在或未连接或被禁用:${agentToolDefine.source}`,
                toolCall.id
            );
        }
    }
}

async function localExecute(toolName: string, toolCallId: string, param: string, agentConvId: string, displayMessageId: string, client: IdeClient): Promise<ToolCallResult> {
    try {
        switch (toolName) {
            case 'grep_search':
                return new ToolCallResult(
                    toolName,
                    param,
                    true,
                    JSON.stringify(grep_search(JSON.parse(param) as GrepSearchParam)),
                    toolCallId
                );
            case 'view_file':
                return new ToolCallResult(
                    toolName,
                    param,
                    true,
                    view_file(JSON.parse(param) as ViewFileParam),
                    toolCallId
                );
            case 'write_to_file':
                write_to_file(JSON.parse(param) as WriteToFileParam, agentConvId, client)
                return new ToolCallResult(
                    toolName,
                    param,
                    true,
                    '',
                    toolCallId
                );
            case 'list_dir':
                return new ToolCallResult(
                    toolName,
                    param,
                    true,
                    JSON.stringify(list_dir(JSON.parse(param))),
                    toolCallId
                );
            case 'codebase_search':
                return new ToolCallResult(
                    toolName,
                    param,
                    true,
                    JSON.stringify(codebase_search(JSON.parse(param))),
                    toolCallId
                );
            case 'related_files':
                return new ToolCallResult(
                    toolName,
                    param,
                    true,
                    JSON.stringify(related_files(JSON.parse(param))),
                    toolCallId
                );
            case 'edit_file':
                await edit_file(JSON.parse(param), toolCallId, agentConvId, displayMessageId, client)
                return new ToolCallResult(
                    toolName,
                    param,
                    true,
                    '',
                    toolCallId
                );
            default:
                return new ToolCallResult(
                    toolName,
                    param,
                    false,
                    '',
                    toolCallId,
                    ''
                );
        }
    } catch (error: any) {
        return new ToolCallResult(
            toolName,
            param,
            false,
            '',
            toolCallId,
            '工具执行失败:' + error.message,
        );
    }
}
