import * as fs from 'fs';
import * as glob from 'glob';
import * as path from 'path';
import { IdeClient } from '../../model/IdeClient';
import { applyCodeUpdate } from '../../request/editMergeRequest';
import { logger } from '../../utils/Logger';
import { EditFileToolCache } from './EditFileToolCache';

export interface WriteToFileParam {
    targetFile: string;
    codeContent: string;
    emptyFile: boolean;
}
export interface EditFileParam {
    targetFile: string;
    codeEdit: string;
}
export interface EditFileResult {
    success: boolean;
    message: string;
}
export interface GrepSearchParam {
    caseInsensitive: boolean;
    includes: string[];
    matchPerLine: boolean;
    query: string;
    searchDirectory: string;
}
export interface SearchResult {
    file: string;
    line?: number;
    snippet?: string;
}
export interface ViewFileParam {
    absolutePath: string;
    startLine: number;
    endLine: number;
}
export interface ListDirParam {
    dirPath: string;
}
export interface DirInfoResult {
    path: string;
    isDir: boolean;
    children: number;
    size: number;
}
export interface RelatedFilesParam {
    absolutePath: string;
}
export interface RelatedFilesResult {
    path: string;
    isDir: boolean;
    children: number;
    size: number;
}
export interface CodebaseSearchParam {
    query: string;
    targetDirectories: string[];
}
export interface CodebaseSearchResult {
    filePath: string;
    content: string;
}
export class ToolExecuteError extends Error {
    constructor(message: string) {
        super(message);
        // 设置异常名称
        this.name = 'ToolExecuteError';
    }
}

/**
 * 快速基于文本的搜索。为了避免输出过多，结果被限制为最多 50 个匹配项。使用"包含"选项可以通过文件类型或特定路径过滤搜索范围，以缩小结果。
 * @param {boolean} CaseInsensitive - 如果为 true，则执行不区分大小写的搜索。目前仅针对搜索内容，不针对搜索的文件路径
 * @param {string[]} Includes - 要搜索的文件或目录。支持文件模式（例如，'*.txt' 表示所有 .txt 文件）或具体路径（例如，'path/to/file.txt' 或 'path/to/dir'）。
 * @param {boolean} MatchPerLine - 如果为 true，则返回每行匹配查询的内容，包括行号和匹配行的代码片段。如果为 false，则仅返回包含查询的文件名。
 * @param {string} Query - 在文件中查找的搜索词或模式。
 * @param {string} SearchDirectory - 运行搜索命令的目录。此路径必须是目录，而不是文件。
 * @returns {SearchResult[]} - 包含搜索结果的对象数组
 */
export function grep_search(param: GrepSearchParam): SearchResult[] {
    if (!param.searchDirectory || !param.query) {
        throw new ToolExecuteError('缺少必要的参数query或searchDirectory');
    }
    if (!fs.existsSync(param.searchDirectory) || !fs.statSync(param.searchDirectory).isDirectory()) {
        throw new ToolExecuteError("搜索目录无效或不是一个目录");
    }
    try {
        const results: SearchResult[] = [];
        const maxResults = 50;

        // 解析文件模式
        const files = param.includes?.flatMap(pattern => {
            if (!pattern) return [];
            return glob.sync(pattern, { cwd: param.searchDirectory, nodir: true });
        });

        // 遍历每个文件
        for (const file of files) {
            if (!file) continue;
            if (results.length >= maxResults) {
                break;
            }
            const filePath = path.join(param.searchDirectory, file);
            const fileContent = fs.readFileSync(filePath, 'utf-8');
            const lines = fileContent.split('\n');
            if (param.caseInsensitive) {
                param.query = param.query.toLowerCase();
            }

            // 搜索文件内容
            if (param.matchPerLine) {
                for (let i = 0; i < lines.length; i++) {
                    let line = lines[i];
                    if (param.caseInsensitive) {
                        line = line.toLowerCase();
                    }
                    if (line.includes(param.query)) {
                        if (results.length >= maxResults) {
                            break;
                        }
                        results.push({
                            file: filePath,
                            line: i + 1, // 行号从1开始
                            snippet: lines[i]
                        });
                    }
                }
            } else {
                for (let i = 0; i < lines.length; i++) {
                    let line = lines[i];
                    if (param.caseInsensitive) {
                        line = line.toLowerCase();
                    }
                    if (line.includes(param.query)) {
                        if (results.length >= maxResults) {
                            break;
                        }
                        results.push({
                            file: filePath,
                        });
                    }
                }
            }
        }

        return results.slice(0, maxResults); // 确保结果不超过50个
    } catch (error: any) {
        // 处理错误
        throw new ToolExecuteError(`搜索失败: ${error.message}`);
    }
}

/**
 * 查看文件内容。
 * 文件的行是从 0 开始索引的，此工具调用的输出将是从 StartLine 到 EndLine 的文件内容，以及 StartLine 和 EndLine 之外的行的摘要。
 * 请注意，此调用每次最多可以查看 200 行。
 * @param {string} AbsolutePath - 要查看的文件的绝对路径。必须是绝对路径。
 * @param {number} StartLine - 要查看的起始行。从 0 开始索引。
 * @param {number} EndLine - 要查看的结束行。此值不能超过 StartLine 的 200 行。
 * @returns {string} - 指定行范围内的文件内容，以及未显示行的摘要
 */
export function view_file(param: ViewFileParam): string {
    if (!param.absolutePath || param.startLine < 0 || param.endLine < 0) {
        throw new ToolExecuteError(`缺少必要的参数absolutePath或startLine与endLine值不合法`);
    }
    if (!fs.existsSync(param.absolutePath) || !fs.statSync(param.absolutePath).isFile()) {
        throw new ToolExecuteError(`文件路径：${param.absolutePath} 无效或不是一个文件`);
    }
    if ((param.endLine - param.startLine) > 200) {
        throw new ToolExecuteError(`endLine不能超过startLine 200行`);
    }
    try {
        const fileContent = fs.readFileSync(param.absolutePath, 'utf-8');
        const lines = fileContent.split('\n');

        let output = '';
        let summary = '';

        if (param.startLine > lines.length) {
            throw new ToolExecuteError(`起始行超出文件范围`);
        }
        if (lines.length <= param.endLine) {
            summary = `<... all lines from file ${param.absolutePath} has shown ...>`;
        } else {
            summary = `<... ${lines.length - (param.endLine - param.startLine + 1)} more lines from ${param.absolutePath} not shown ...>`;
        }

        for (let i = param.startLine; i < param.endLine; i++) {
            if (i < lines.length) {
                output += lines[i] + '\n';
            }
        }

        output += summary;
        return output;
    } catch (error: any) {
        throw new ToolExecuteError(`文件 ${param.absolutePath} 查看失败: ${error.message}`);
    }
}

/**
 * 使用此工具创建新文件。如果文件或其父目录不存在，将为你创建。
 * @param {string} TargetFile - 要创建并写入代码的目标文件。
 * @param {string} CodeContent - 要写入文件的代码内容。
 * @param {boolean} EmptyFile - 如果为 true，则创建一个空文件。
 * @returns {WriteToFileResult} - 表示文件创建成功或失败的对象
 */
export function write_to_file(param: WriteToFileParam,
    agentConvId: string,
    client: IdeClient): void {
    if (!param.targetFile || !param.codeContent) {
        throw new ToolExecuteError("缺少必要的参数:targetFile,codeContent");
    }
    // 检查目标文件是否已经存在
    if (fs.existsSync(param.targetFile)) {
        throw new ToolExecuteError("文件已存在，无法覆盖：" + param.targetFile);
    }
    try {
        // 创建父目录（如果需要）
        const parentDir = path.dirname(param.targetFile);
        if (!fs.existsSync(parentDir)) {
            const result = fs.mkdirSync(parentDir, { recursive: true });
            if (!result) {
                throw new ToolExecuteError(`文件 ${param.targetFile} 的父目录 ${parentDir} 创建失败`);
            }
        }
    } catch (error: any) {
        throw new ToolExecuteError(`文件 ${param.targetFile} 创建失败: ${error.message}`);
    }
    try {
        // 写入文件内容
        if (!param.emptyFile) {
            fs.writeFileSync(param.targetFile, param.codeContent);
        }
    } catch (error: any) {
        throw new ToolExecuteError(`文件 ${param.targetFile} 写入失败: ${error.message}`);
    }
    try {
        // 从绝对路径 target_file 中截取出文件名称
        const fileName = path.basename(param.targetFile);
        // 优化路径拼接，去除多余斜杠
        let ideDir = client.getIdeDir();
        // 去除末尾所有斜杠
        ideDir = ideDir.replace(/[\\/]+$/, '');
        const cacheDir = path.join(ideDir, agentConvId);
        if (!fs.existsSync(cacheDir)) {
            fs.mkdirSync(cacheDir, { recursive: true });
        }
        // 创建文件
        if (!client.agentStorge.getOrigiFileContentMap().has(param.targetFile)) {
            const originFileCachePath = path.join(ideDir, agentConvId, fileName);
            if (!fs.existsSync(originFileCachePath)) {
                fs.writeFileSync(originFileCachePath, "", 'utf-8');
            }
            client.agentStorge.getOrigiFileContentMap().set(param.targetFile, {
                filePath: param.targetFile,
                originFileCachePath: originFileCachePath,
                diffCount: 10000
            });
        }
    } catch (error: any) {
        throw new ToolExecuteError(`对比空文件 ${param.targetFile} 创建失败: ${error.message}`);
    }
}

/**
 * 使用此工具对原始文件和修改内容进行合并操作
 * @param {string} target_file - 原始文件路径
 * @param {string} code_edit - 修改代码块
 * @returns {EditFileResult} - 表示合并操作结果
 */
export function edit_file(param: EditFileParam,
    toolCallId: string,
    agentConvId: string,
    displayMessageId: string,
    client: IdeClient): Promise<void> {
    if (!param.targetFile || !param.codeEdit) {
        throw new ToolExecuteError("缺少必要的参数:targetFile,codeEdit");
    }
    try {
        // 以utf-8编码读取原始内容
        const originContent = fs.readFileSync(param.targetFile, 'utf-8');
        // 合并内容
        return applyCodeUpdate(client.baseUrl, originContent, param.codeEdit).then(mergeContent => {
            if (mergeContent === "") {
                throw new ToolExecuteError(`文件 ${param.targetFile} 编辑失败: 合并修改内容失败`);
            }
            // 用新内容覆盖原文件
            fs.writeFileSync(param.targetFile, mergeContent, 'utf-8');
            // 缓存数据到本地路径下的文件
            const cache = new EditFileToolCache(agentConvId,
                displayMessageId,
                toolCallId,
                param.targetFile,
                originContent,
                param.codeEdit);
            // 从绝对路径 target_file 中截取出文件名称
            const fileName = path.basename(param.targetFile);
            // 优化路径拼接，去除多余斜杠
            let ideDir = client.getIdeDir();
            // 去除末尾所有斜杠
            ideDir = ideDir.replace(/[\\/]+$/, '');
            const cacheDir = path.join(ideDir, agentConvId, toolCallId);
            if (!fs.existsSync(cacheDir)) {
                fs.mkdirSync(cacheDir, { recursive: true });
            }
            fs.writeFileSync(path.join(cacheDir, fileName), JSON.stringify(cache), 'utf-8');
            // 缓存原始文件内容，只有在原始文件不存在时才需要缓存。
            // 工具本身执行并不会刷新原始文件内容，只有当用户触发接受拒绝操作时，才会刷新
            if (!client.agentStorge.getOrigiFileContentMap().has(param.targetFile)) {
                const originFileCachePath = path.join(ideDir, agentConvId, fileName);
                if (!fs.existsSync(originFileCachePath)) {
                    fs.writeFileSync(originFileCachePath, originContent, 'utf-8');
                }
                client.agentStorge.getOrigiFileContentMap().set(param.targetFile, {
                    filePath: param.targetFile,
                    originFileCachePath: originFileCachePath,
                    diffCount: 10000
                });
            }
        }).catch((error: any) => {
            throw error;
        });
    } catch (error: any) {
        return Promise.reject(new ToolExecuteError(`文件 ${param.targetFile} 编辑失败: ${error.message}`));
    }
}

/**
 * 读取指令目录下的文件列表信息
 * @param listDirParam {dirPath: string} - 目录路径
 * @returns 文件对象列表{name:文件路径,type:文件类型}，文件类型包括 file, directory, other
 * @throws {ToolExecuteError} - 如果目录不存在或读取失败，将抛出异常
 */
export function list_dir(param: ListDirParam): DirInfoResult[] {
    if (!param.dirPath) {
        throw new ToolExecuteError(`缺少必要的参数:dirPath`);
    }
    if (!fs.existsSync(param.dirPath) || !fs.statSync(param.dirPath).isDirectory()) {
        throw new ToolExecuteError(`目录 ${param.dirPath} 不存在或不是一个目录`);
    }
    try {
        const files = fs.readdirSync(param.dirPath, {
            withFileTypes: true,
            recursive: false
        });
        return files.map(file => ({
            path: path.join(param.dirPath, file.name),
            isDir: file.isDirectory(),
            children: file.isDirectory() ? fs.readdirSync(path.join(param.dirPath, file.name), { withFileTypes: true }).length : 0,
            size: file.isDirectory() ? 0 : fs.statSync(path.join(param.dirPath, file.name)).size,
        }));
    } catch (error: any) {
        throw new ToolExecuteError(`读取目录 ${param.dirPath} 失败: ${error.message}`);
    }
}

/**
 * 获取指定文件所在目录下的其他相关文件
 * @param absolutePath 文件的绝对路径
 * @returns 相关文件的路径列表，以换行符分隔
 */
export function related_files(param: RelatedFilesParam): RelatedFilesResult[] {
    // 校验文件路径是否为空
    if (!param.absolutePath) {
        throw new ToolExecuteError(`缺少必要的参数:absolutePath`);
    }
    if (!fs.existsSync(param.absolutePath) || !fs.statSync(param.absolutePath).isFile()) {
        throw new ToolExecuteError(`文件路径：${param.absolutePath} 无效或不是一个文件`);
    }
    // 返回值初始化
    let results: RelatedFilesResult[] = [];
    try {
        // 获取文件所在目录
        const parentDir = path.dirname(param.absolutePath);
        // 读取目录内容
        const files = fs.readdirSync(parentDir, { withFileTypes: true });
        // 遍历文件列表
        for (const file of files) {
            const filePath = path.join(parentDir, file.name);
            // 加入与输入文件不同的其他文件的路径到结果
            if (path.resolve(filePath) !== path.resolve(param.absolutePath)) {
                const fileStat = fs.statSync(filePath);
                const result = {
                    path: filePath,
                    isDir: fileStat.isDirectory(),
                    children: fileStat.isDirectory() ? fs.readdirSync(filePath, { withFileTypes: true }).length : 0,
                    size: fileStat.isDirectory() ? 0 : fileStat.size,
                }
                results.push(result);
            }
        }
    } catch (error: any) {
        throw new ToolExecuteError(`获取相关文件失败: ${error.message}`);
    }
    return results;
}

/**
 * 代码搜索
 * @param query 搜索查询的关键词 
 * @param targetDirectories 要搜索的目录的绝对路径列表 
 * @returns 
 */
export function codebase_search(param: CodebaseSearchParam): CodebaseSearchResult[] {
    if (!param.query || !param.targetDirectories || param.targetDirectories.length === 0) {
        throw new ToolExecuteError(`缺少必要的参数:query,targetDirectories`);
    }
    const results: CodebaseSearchResult[] = [];
    const maxResults = 50;

    for (const directory of param.targetDirectories) {
        try {
            const stats = fs.statSync(directory);
            if (stats.isDirectory()) {
                codeBaseSearchInDirectory(directory, param.query, results, maxResults);
            }
        } catch (error) {
            logger.warn(`访问目录 ${directory} 时出错: ${error}`);
        }
    }

    return results;
}

function codeBaseSearchInDirectory(directory: string, query: string, results: CodebaseSearchResult[], maxResults: number): void {
    try {
        const files = fs.readdirSync(directory, { withFileTypes: true });
        for (const file of files) {
            if (results.length >= maxResults) {
                break;
            }
            const fullPath = path.join(directory, file.name);
            if (file.isDirectory()) {
                codeBaseSearchInDirectory(
                    fullPath,
                    query,
                    results,
                    maxResults
                );
            } else {
                try {
                    const content = fs.readFileSync(fullPath, 'utf-8');
                    const lines = content.split('\n');
                    for (const line of lines) {
                        if (line.includes(query)) {
                            const result: CodebaseSearchResult = {
                                filePath: fullPath,
                                content: line
                            };
                            if (results.length >= maxResults) {
                                break;
                            }
                            results.push(result);
                        }
                    }
                } catch (error) {
                    logger.error(`读取文件 ${fullPath} 时出错: ${error}`);
                }
            }
        }
    } catch (error) {
        logger.error(`读取目录 ${directory} 时出错: ${error}`);
    }
}