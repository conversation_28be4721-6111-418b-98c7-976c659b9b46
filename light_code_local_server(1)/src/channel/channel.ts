import { MessageType } from "../server/MessageType";
import { createMessage, GrpcMessage } from "../server/grpcMessage";
import { logger } from "../utils/Logger";
import { IDE_VERSION, LIGHT_CODE_VERSION, GlobalStore, PROCESS_VERSION, IDE_CLIENT } from "../GlobalStore";
import { CLIENT_NOT_INIT, ILLEGAL_MESSAGE_TYPE } from "../consts/ErrorCode";
import { IdeClient } from "../model/IdeClient";

class ChannelCheckMessage {
    ideVersion: string;
    lightCodeVersion: string;
    processVersion: string;
    constructor(ideVersion: string, lightCodeVersion: string, processVersion: string) {
        this.ideVersion = ideVersion;
        this.lightCodeVersion = lightCodeVersion;
        this.processVersion = processVersion;
    }
}
export function routeSimple(message: GrpcMessage, callback: (error: Error | null, response: any) => void): void {
    if (message.messageType === MessageType.ide_to_proc_check_req) {
        ideCheck(message, callback);
    } else if (message.messageType === MessageType.ide_to_proc_heart_beat_req) {
        heartBeat(message, callback);
    } else {
        logger.error(`收到未知channel处理消息: ${JSON.stringify(message)}`);
        const errorMessage = createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_error_illegal_message_type,
            "未知的消息类型：" + message.messageType,
            ILLEGAL_MESSAGE_TYPE
        );
        callback(null, errorMessage);
    }
}

function heartBeat(message: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    logger.debug(`收到客户端心跳消息, 客户端: ${message.sender}`);
    const ideClient = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(message.sender) as IdeClient;
    // 如果客户端未初始化，则返回错误
    if (!ideClient || !ideClient.init) {
        logger.error(`IDE尚未注册到进程, 客户端: ${message.sender}, 消息: ${JSON.stringify(message)}`);
        const errorMessage = createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_error_client_not_init,
            "IDE尚未注册到进程",
            CLIENT_NOT_INIT
        );
        callback(null, errorMessage);
    } else {
        // 更新最后一次心跳时间
        ideClient.lastHeartBeatTime = Date.now();
        callback(null, createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_heart_beat_resp,
            "success"
        ));
    }
}

function ideCheck(message: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    logger.info(`收到客户端检查消息, 客户端: ${message.sender}`);

    // TODO:测试代码
    // GlobalStore.getStore().setStaticValue(IDE_VERSION, "IntelliJ IDEA 2024.1.6");
    // GlobalStore.getStore().setStaticValue(LIGHT_CODE_VERSION, "agent-process-beta-0411");

    // GlobalStore.getStore().setStaticValue(IDE_VERSION, "VS Code 1.99.3");
    // GlobalStore.getStore().setStaticValue(LIGHT_CODE_VERSION, "2.0.0");
    const ideVersion = GlobalStore.getStore().getStaticValue(IDE_VERSION);
    const lightCodeVersion = GlobalStore.getStore().getStaticValue(LIGHT_CODE_VERSION);
    const processVersion = GlobalStore.getStore().getStaticValue(PROCESS_VERSION);
    logger.info(`当前进程对应的IDE版本: ${ideVersion}，LightCode版本: ${lightCodeVersion}，Process版本: ${processVersion}`);
    callback(null, createMessage(
        message.taskId,
        message.requestId,
        message.moduleType,
        MessageType.proc_to_ide_check_resp,
        JSON.stringify(new ChannelCheckMessage(
            ideVersion,
            lightCodeVersion,
            processVersion
        ))
    ));
}

