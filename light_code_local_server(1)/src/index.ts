// 添加 Event 的 polyfill
interface EventInterface {
  type: string;
  bubbles: boolean;
  cancelable: boolean;
  composed: boolean;
  defaultPrevented: boolean;
  preventDefault(): void;
}

interface CustomEventInterface extends EventInterface {
  detail: any;
}

// 添加 EventTarget 的 polyfill
if (typeof global.EventTarget === 'undefined') {
  class EventTarget {
    private listeners: { [type: string]: ((event: EventInterface) => void)[] } = {};

    addEventListener(type: string, listener: (event: EventInterface) => void) {
      if (!this.listeners[type]) {
        this.listeners[type] = [];
      }
      this.listeners[type].push(listener);
    }

    removeEventListener(type: string, listener: (event: EventInterface) => void) {
      if (!this.listeners[type]) return;
      const index = this.listeners[type].indexOf(listener);
      if (index !== -1) {
        this.listeners[type].splice(index, 1);
      }
    }

    dispatchEvent(event: EventInterface) {
      if (!this.listeners[event.type]) return true;
      this.listeners[event.type].forEach(listener => {
        listener(event);
      });
      return !event.cancelable || !event.defaultPrevented;
    }
  }
  global.EventTarget = EventTarget as any;
}

if (typeof global.Event === 'undefined') {
  const Event = function (this: EventInterface, type: string, eventInitDict?: EventInit) {
    this.type = type;
    this.bubbles = eventInitDict?.bubbles ?? false;
    this.cancelable = eventInitDict?.cancelable ?? false;
    this.composed = eventInitDict?.composed ?? false;
    this.defaultPrevented = false;
  } as any;
  Event.prototype = {
    type: '',
    bubbles: false,
    cancelable: false,
    composed: false,
    defaultPrevented: false,
    preventDefault() {
      if (this.cancelable) {
        this.defaultPrevented = true;
      }
    }
  };
  global.Event = Event;
}

if (typeof global.CustomEvent === 'undefined') {
  const CustomEvent = function (type: string, eventInitDict?: CustomEventInit) {
    const event = new (global.Event as any)(type, eventInitDict) as CustomEventInterface;
    event.detail = eventInitDict?.detail ?? null;
    return event;
  } as any;
  global.CustomEvent = CustomEvent;
}

// 在运行时创建临时的 package.json
import * as fs from 'fs';
import * as path from 'path';
import { DEBUG, GlobalStore, HEART_BEAT_INTERVAL, IDE_VERSION, LIGHT_CODE_VERSION, LOG_DIR, PORT } from './GlobalStore';
import { logger } from './utils/Logger';

// 解析命令行参数
const args = process.argv.slice(2);
if (args[0]) {
  GlobalStore.getStore().setStaticValue(PORT, parseInt(args[0]));
}
if (args[1]) {
  GlobalStore.getStore().setStaticValue(LOG_DIR, args[1]);
}
if (args[2]) {
  GlobalStore.getStore().setStaticValue(DEBUG, args[2] === 'true');
} else {
  GlobalStore.getStore().setStaticValue(DEBUG, false);
}
if (args[3]) {
  GlobalStore.getStore().setStaticValue(IDE_VERSION, args[3]);
}
if (args[4]) {
  GlobalStore.getStore().setStaticValue(LIGHT_CODE_VERSION, args[4]);
}
if (args[5]) {
  GlobalStore.getStore().setStaticValue(HEART_BEAT_INTERVAL, parseInt(args[5]));
} else {
  GlobalStore.getStore().setStaticValue(HEART_BEAT_INTERVAL, 10000);
}

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));
logger.info(`准备启动LightCode进程服务，
  端口: ${GlobalStore.getStore().getStaticValue(PORT)}, 
  日志目录: ${GlobalStore.getStore().getStaticValue(LOG_DIR)},
  IDE版本: ${GlobalStore.getStore().getStaticValue(IDE_VERSION)},
  LightCode版本: ${GlobalStore.getStore().getStaticValue(LIGHT_CODE_VERSION)}`);

// 创建临时的 package.json
const packageJson = {
  name: 'lightcode',
  version: '1.0.0',
  main: 'lightcode.js',
  type: 'commonjs',
  private: true
};

fs.writeFileSync(path.join(GlobalStore.getStore().getStaticValue(LOG_DIR), 'package.json'), JSON.stringify(packageJson, null, 2));

// 设置 NODE_PATH 环境变量
process.env.NODE_PATH = GlobalStore.getStore().getStaticValue(LOG_DIR);

function initServer() {
  // 导入并运行服务器
  require('./server/server');
  // 导入并运行工具加载器
  require('./agent/tool/toolLoader');
  // 导入并运行内部任务
  require('./task/innerTask');
}

initServer(); 
