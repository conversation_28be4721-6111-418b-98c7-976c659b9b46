import { CLIENT_NOT_INIT, ILLEGAL_MESSAGE_TYPE } from "../consts/ErrorCode";
import { GlobalStore, IDE_CLIENT } from "../GlobalStore";
import { IdeClient } from "../model/IdeClient";
import { createMessage, GrpcMessage } from "../server/grpcMessage";
import { MessageType } from "../server/MessageType";
import { logger } from "../utils/Logger";
import { TokenUtils } from "../utils/tokenUtil";

interface TokenCalcMessage {
    content: string;
    wordlistName: string;
}
interface ImageTokenCalculationMessage {
    imageAlgoType: string;
    width: number;
    height: number;
    detail: string;
}
interface TokenCutMessage {
    content: string;
    wordlistName: string;
    maxTokens: number;
}

interface TokenInitMessage {
    wordlistName: string;
}


export function routeSimple(message: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const ideClient = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(message.sender) as IdeClient;
    // 如果客户端未初始化，则返回错误
    if (!ideClient || !ideClient.init) {
        const errorMessage = createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_error_client_not_init,
            "IDE尚未注册到进程",
            CLIENT_NOT_INIT
        );
        callback(null, errorMessage);
        return;
    }
    if (message.messageType === MessageType.ide_to_proc_token_calc_req) {
        // 计算token数量请求
        const { content, wordlistName } = JSON.parse(message.content) as TokenCalcMessage;
        TokenUtils.token(content, wordlistName).then(tokens => {
            const responseMessage = createMessage(
                message.taskId,
                message.requestId,
                message.moduleType,
                MessageType.proc_to_ide_token_calc_resp,
                JSON.stringify({ tokens: tokens })
            );
            callback(null, responseMessage);
        });
    } else if (message.messageType === MessageType.ide_to_proc_image_token_calc_req) {
        // 计算图片token数量请求
        const { imageAlgoType, width, height, detail } = JSON.parse(message.content) as ImageTokenCalculationMessage;
        const tokens = TokenUtils.getImageTokens(imageAlgoType, width, height, detail);
        const responseMessage = createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_image_token_calc_resp,
            JSON.stringify({ tokens: tokens })
        );
        callback(null, responseMessage);
    } else if (message.messageType === MessageType.ide_to_proc_token_cut_req) {
        // 截取token数量请求
        const { content, wordlistName, maxTokens } = JSON.parse(message.content) as TokenCutMessage;
        TokenUtils.tokensCut(content, wordlistName, maxTokens).then(result => {
            const responseMessage = createMessage(
                message.taskId,
                message.requestId,
                message.moduleType,
                MessageType.proc_to_ide_token_cut_resp,
                JSON.stringify(result)
            );
            callback(null, responseMessage);
        });
    } else if (message.messageType === MessageType.ide_to_proc_token_init_req) {
        // 初始化token词库
        const { wordlistName } = JSON.parse(message.content) as TokenInitMessage;
        TokenUtils.loadWordList(wordlistName);
        const responseMessage = createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_token_init_resp,
            ''
        );
        callback(null, responseMessage);

    } else {
        logger.error(`收到未知llmModel处理消息: ${JSON.stringify(message)}`);
        const errorMessage = createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_error_illegal_message_type,
            "未知的消息类型。功能模块：" + message.moduleType + "，消息类型：" + message.messageType,
            ILLEGAL_MESSAGE_TYPE
        );
        callback(null, errorMessage);
    }
}
