export type McpConfig = SSEMcpServerConfig | StdioMcpServerConfig;

// 定义传输类型
type McpTransportType = "stdio" | "sse"

// 基础配置接口
interface McpServerConfig {
	id: string;
	name: string;
	timeout?: number;
	disabled?: boolean;
	status?: "connected" | "connecting" | "disconnected";
	errorMsg?: string;
}
interface SSEMcpServerConfig extends McpServerConfig {
	transportType: "sse";
	url: string;
	apikey?: string;
}
interface StdioMcpServerConfig extends McpServerConfig {
	transportType: "stdio";
	command: string;
	args: string[];
	env: Map<string, string>;
}
