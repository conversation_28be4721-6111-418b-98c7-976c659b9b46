import { Client } from "@modelcontextprotocol/sdk/client/index.js"
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js"
import {
	CallToolResultSchema,
} from "@modelcontextprotocol/sdk/types.js"
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js"
import { McpServer } from "./McpServer"
import { logger } from "../utils/Logger"

// 默认MCP超时时间
const DEFAULT_MCP_TIMEOUT = 60000

export class McpConnection {
    server: McpServer
    client: Client
    transport: StdioClientTransport | SSEClientTransport
    constructor(server: McpServer, client: Client, transport: StdioClientTransport | SSEClientTransport) {
        this.server = server
        this.client = client
        this.transport = transport
    }
    async callTool(toolName: string, toolArguments?: Record<string, unknown>): Promise<McpToolCallResponse> {
        let timeout = DEFAULT_MCP_TIMEOUT;
        if (this.server.timeout) {
            timeout = this.server.timeout * 1000;
        }
        try {
            const result = await this.client.request(
                {
                    method: "tools/call",
                    params: {
                        name: toolName,
                        arguments: toolArguments,
                    },
                },
                CallToolResultSchema,
                {
                    timeout,
                },
            )
            return result as McpToolCallResponse
        } catch (error) {
            logger.error(`调用 ${this.server.name} 的工具失败: ${error}`);
            throw error;
        }
    }
}

type McpToolCallResponse = {
    _meta?: Record<string, any>
    content: Array<
        | {
            type: "text"
            text: string
        }
        | {
            type: "image"
            data: string
            mimeType: string
        }
        | {
            type: "resource"
            resource: {
                uri: string
                mimeType?: string
                text?: string
                blob?: string
            }
        }
    >
    isError?: boolean
}