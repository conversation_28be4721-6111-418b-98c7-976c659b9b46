import { Client } from "@modelcontextprotocol/sdk/client/index.js"
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js"
import { StdioClientTransport } from "@modelcontextprotocol/sdk/client/stdio.js"
import {
	ListPromptsResultSchema,
	ListResourcesResultSchema,
	ListResourceTemplatesResultSchema,
	ListToolsResultSchema,
	ReadResourceResultSchema,
} from "@modelcontextprotocol/sdk/types.js"
import { ToolDefine } from "../agent/tool/ToolDefine"
import { Tool } from "../model/LLModel"
import { logger } from "../utils/Logger"
import { McpConfig } from "./McpConfig"
import { McpConnection } from "./McpConnection"
// 默认请求超时时间
const DEFAULT_REQUEST_TIMEOUT_MS = 5000

type McpTool = {
	name: string
	description?: string
	inputSchema?: object
	autoApprove?: boolean
}

type McpPrompt = {
	name: string
	description?: string
	arguments?: object
}

type McpResource = {
	uri: string
	name: string
	mimeType?: string
	description?: string
}

type McpResourceTemplate = {
	uriTemplate: string
	name: string
	description?: string
	mimeType?: string
}

type McpResourceResponse = {
	_meta?: Record<string, any>
	contents: Array<{
		uri: string
		mimeType?: string
		text?: string
		blob?: string
	}>
}

export class McpHub {
	connections: Map<string, McpConnection> = new Map()
	// 工具名称到MCP服务器的映射
	toolToMcpServer: Map<string, McpConnection> = new Map()
	// 工具名称到MCP服务器的ID的映射
	toolToMcpTool: Map<string, string> = new Map()

	constructor() {
	}

	async connectToServer(config: McpConfig,): Promise<McpConnection> {
		try {
			const client = new Client(
				{
					name: "Cline",
					version: "1.0.0",
				},
				{
					capabilities: {},
				},
			)

			let transport: StdioClientTransport | SSEClientTransport

			if (config.transportType && config.transportType === "sse") {
				transport = new SSEClientTransport(new URL(config.url), {})
			} else {
				transport = new StdioClientTransport({
					command: config.command,
					args: config.args,
					env: {
						...Object.fromEntries(config.env),
						...(process.env.PATH ? { PATH: process.env.PATH } : {}),
					},
					stderr: "pipe",
				})
			}

			transport.onerror = async (error) => {
				logger.error(`${config.name} 的传输错误: ${error}`);
				const connection = this.connections.get(config.id)
				if (connection) {
					connection.server.status = "disconnected"
					this.appendErrorMessage(connection, error.message)
				}
			}

			transport.onclose = async () => {
				const connection = this.connections.get(config.id)
				if (connection) {
					connection.server.status = "disconnected"
				}
			}

			const connection = new McpConnection(
				{
					id: config.id,
					name: config.name,
					config: JSON.stringify(config),
					status: "connecting",
				},
				client,
				transport,
			)
			if (config.id) {
				this.connections.set(config.id, connection)
			}

			if (config.transportType && config.transportType === "stdio") {
				await transport.start()
				const stderrStream = (transport as StdioClientTransport).stderr
				if (stderrStream) {
					stderrStream.on("data", async (data: Buffer) => {
						const output = data.toString()
						const isInfoLog = /^\s*INFO\b/.test(output)

						if (isInfoLog) {
							logger.info(`Server "${config.name}" info:${output}`)
						} else {
							logger.error(`Server "${config.name}" stderr:${output}`)
							const connection = this.connections.get(config.id)
							if (connection) {
								this.appendErrorMessage(connection, output)
							}
						}
					})
				} else {
					logger.error(`No stderr stream for ${config.name}`)
				}
				transport.start = async () => { }
			}
			const connectPromise = client.connect(transport)
			const timeoutPromise = new Promise((_, reject) =>
				setTimeout(() => reject(new Error("连接超时")), 20000) // 20秒超时
			)
			await Promise.race([connectPromise, timeoutPromise])
			// await client.connect(transport)
			logger.info(`连接到 ${config.name} 成功`)
			connection.server.status = "connected"
			connection.server.error = ""
			return connection
		} catch (error) {
			const connection = this.connections.get(config.id)
			if (connection) {
				connection.server.status = "disconnected"
				this.appendErrorMessage(connection, error instanceof Error ? error.message : String(error))
			}
			throw error
		}
	}

	async checkServer(config: McpConfig): Promise<boolean> {
		const connection = this.connections.get(config.id)
		try {
			if (!connection) {
				await this.connectToServer(config);
				return true;
			}
			// 尝试获取工具列表来验证服务是否正常响应
			await this.fetchToolsList(connection)
			return true
		} catch (error) {
			logger.error(`检查服务器 ${config.name} 可用性失败: ${error instanceof Error ? error.message : String(error)}`)
			if (connection) {
				connection.server.status = "disconnected"
				this.appendErrorMessage(connection, error instanceof Error ? error.message : String(error))
			}
			return false
		}
	}

	findUniqueName(name: string): string {
		let uniqueName = name;
		let suffix = 1;
		while (this.toolToMcpServer.has(uniqueName)) {
			uniqueName = `${name}_${suffix}`;
			suffix++;
		}
		this.toolToMcpTool.set(uniqueName, name);
		return uniqueName;
	}

	appendErrorMessage(connection: McpConnection, error: string) {
		const newError = connection.server.error ? `${connection.server.error}\n${error}` : error
		connection.server.error = newError
	}

	async deleteConnection(name: string): Promise<void> {
		const connection = this.connections.get(name)
		if (connection) {
			try {
				await connection.transport.close()
				await connection.client.close()
			} catch (error) {
				logger.error(`关闭 ${name} 的传输失败: ${error instanceof Error ? error.message : String(error)}`)
			}
			// 删除工具类到MCP服务器的映射
			for (const toolName of connection.server.toolDefines?.map((tool) => tool.name) || []) {
				this.toolToMcpServer.delete(toolName)
			}
			this.connections.delete(name)
		}
	}

	async deleteServerConnections(newServers: McpConfig[]): Promise<void> {
		const currentNames = Array.from(this.connections.keys())
		const newNames = new Set(newServers.map((server) => server.id))
		for (const name of currentNames) {
			if (!newNames.has(name)) {
				await this.deleteConnection(name)
				logger.info(`删除 MCP 服务器: ${name}`)
			}
		}
	}

	async addServerConnection(config: McpConfig): Promise<McpConnection> {
		try {
			if (config.transportType === "stdio") {
				// this.setupFileWatcher(config.name, config)
			}
			this.connections.delete(config.id)
			const connection = await this.connectToServer(config)
			if (config.id) {
				this.connections.set(config.id, connection)
			}
			const toolDefines = await this.loadData(connection, config);
			toolDefines.forEach((toolDefine) => {
				this.toolToMcpServer.set(toolDefine.name, connection);
			})
			connection.server.toolDefines = toolDefines;
			return connection
		} catch (error) {
			throw error;
		}
	}

	async loadData(connection: McpConnection, config: McpConfig) {
		// 加载工具类
		const mcpTools = await this.fetchToolsList(connection)
		const toolDefines: ToolDefine[] = [];
		for (const mcpTool of mcpTools) {
			const name = this.findUniqueName(mcpTool.name);
			const toolDes: Tool = {
				type: "function",
				function: {
					description: mcpTool.description || '',
					name: name,
					parameters: JSON.parse(JSON.stringify(mcpTool.inputSchema))
				}
			}
			const toolDefine = new ToolDefine(name, config.id, toolDes, mcpTool.name);
			toolDefines.push(toolDefine)
		}
		// 当前实现，prompt也认为是一个工具
		const prompts = await this.fetchPromptList(connection)
		for (const prompt of prompts) {
			const name = this.findUniqueName(prompt.name);
			const toolDes: Tool = {
				type: "function",
				function: {
					description: prompt.description || '',
					name: prompt.name,
					parameters: JSON.parse(JSON.stringify(prompt.arguments))
				}
			}
			const toolDefine = new ToolDefine(name, config.id, toolDes, prompt.name);
			toolDefines.push(toolDefine)
		}

		// connection.server.resources = await this.fetchResourcesList(name)
		// connection.server.resourceTemplates = await this.fetchResourceTemplatesList(name)
		return toolDefines;
	}

	async fetchToolsList(connection: McpConnection): Promise<McpTool[]> {
		try {
			logger.info(`获取 ${connection.server.name} 的工具列表`)
			const response = await connection.client.request({ method: "tools/list" }, ListToolsResultSchema, {
				timeout: DEFAULT_REQUEST_TIMEOUT_MS,
			})
			logger.info(`获取 ${connection.server.name} 的工具列表成功. 工具数量: ${response?.tools?.length}`)
			return response?.tools
		} catch (error) {
			logger.error(`获取 ${connection.server.name} 的工具列表失败: ${error}`);
			throw error;
		}
	}

	async fetchPromptList(connection: McpConnection): Promise<McpPrompt[]> {
		try {
			logger.info(`获取 ${connection.server.name} 的prompt列表`)
			const response = await connection.client.request({ method: "prompts/list" }, ListPromptsResultSchema, {
				timeout: DEFAULT_REQUEST_TIMEOUT_MS,
			})
			logger.info(`获取 ${connection.server.name} 的prompt列表成功. prompt数量: ${response?.prompts?.length}`)
			return response?.prompts
		} catch (error) {
			// 不是所有mcp服务都支持prompt，所以这里不抛出错误
			if (error instanceof Error && error.message.includes("Method not found")) {
				logger.warn(`获取 ${connection.server.name} 的prompt列表失败: ${error}`);
				return []
			}
			logger.error(`获取 ${connection.server.name} 的prompt列表失败: ${error}`);
			throw error;
		}
	}

	async fetchResourcesList(connection: McpConnection): Promise<McpResource[]> {
		try {
			const response = await connection.client.request({ method: "resources/list" }, ListResourcesResultSchema, { timeout: DEFAULT_REQUEST_TIMEOUT_MS })
			return response?.resources || []
		} catch (error) {
			logger.error(`获取 ${connection.server.name} 的资源列表失败: ${error}`);
			throw error;
		}
	}

	async fetchResourceTemplatesList(connection: McpConnection): Promise<McpResourceTemplate[]> {
		try {
			const response = await connection.client.request({ method: "resources/templates/list" }, ListResourceTemplatesResultSchema, {
				timeout: DEFAULT_REQUEST_TIMEOUT_MS,
			})

			return response?.resourceTemplates || []
		} catch (error) {
			logger.error(`获取 ${connection.server.name} 的资源模板列表失败: ${error}`);
			throw error;
		}
	}

	async readResource(connection: McpConnection, uri: string): Promise<McpResourceResponse> {
		try {
			const result = await connection.client.request(
				{
					method: "resources/read",
					params: {
						uri,
					},
				},
				ReadResourceResultSchema,
			)
			return result as McpResourceResponse
		} catch (error) {
			logger.error(`读取 ${connection.server.name} 的资源失败: ${error}`);
			return {
				contents: [],
				_meta: {},
			}
		}
	}

	async deleteServer(serverName: string) {
		const connection = this.connections.get(serverName)
		if (connection) {
			try {
				await connection.transport.close()
				await connection.client.close()
			} catch (error) {
				logger.error(`关闭 ${serverName} 的传输失败: ${error}`);
			}
			this.connections.delete(serverName)
		}
	}
}
