// 添加 AbortController polyfill
if (typeof global.AbortController === 'undefined') {
    const { AbortController } = require('abort-controller');
    global.AbortController = AbortController;
}

import { logger } from '../utils/Logger';
import { McpConfig } from './McpConfig';
import { IdeClient } from '../model/IdeClient';
import { ToolCallResult } from '../agent/tool/ToolCallResult';
import { McpConnection } from './McpConnection';
/**
 * 加载MCP服务器
 * @param mcpConfigs 
 */
export async function loadMcp(client: IdeClient): Promise<void> {
    const mcpConfigs: McpConfig[] = Array.from(client.mcpConfigs.values());
    if (!mcpConfigs || mcpConfigs.length == 0) {
        return;
    }
    let mcpHub = client.mcpHub;
    // 移除掉已经不存在的MCP服务器
    mcpHub.deleteServerConnections(mcpConfigs);
    // 创建新的MCP服务器
    // 加载新的MCP工具和提示词
    for (const config of mcpConfigs) {
        if (config.status && mcpHub.connections.has(config.id)) {
            config.status = mcpHub.connections.get(config.id)?.server.status;
            config.errorMsg = mcpHub.connections.get(config.id)?.server.error;
            continue;
        }
        try {
            const mcpConnection = await mcpHub.addServerConnection(config);
            config.status = mcpConnection.server.status;
        } catch (error) {
            config.status = "disconnected";
            config.errorMsg = error instanceof Error ? error.message : String(error);
            logger.error(`添加 MCP 服务器 ${config.name} 失败: ${error instanceof Error ? error.message : String(error)}`);
        }
    }
}

/**
 * 执行MCP工具并返回结果
 * @param toolName 工具名称
 * @param toolCallId 工具调用ID
 * @param param 工具参数
 * @returns 工具执行结果
 */
export async function executeMcpToolWithCallback(
    toolName: string,
    toolCallId: string,
    param: any,
    mcpConnection: McpConnection
): Promise<ToolCallResult> {
    try {
        // 解析参数
        const params = typeof param === 'string' ? JSON.parse(param) : param;
        // 执行MCP工具
        const result = await mcpConnection.callTool(toolName, params);

        // 返回结果
        return new ToolCallResult(
            toolName,
            JSON.stringify(param),
            !result.isError,
            result.isError ? '' : JSON.stringify(result.content),
            toolCallId,
            result.isError ? JSON.stringify(result.content) : ''
        );
    } catch (error) {
        return new ToolCallResult(
            toolName,
            JSON.stringify(param),
            false,
            '',
            toolCallId,
            `MCP工具执行失败: ${error instanceof Error ? error.message : String(error)}`
        );
    }
}
