import path from "path";
import { AgentDefine } from "../agent/AgentDefine";
import { AgentStorage } from "../agent/AgentStorage";
import { ToolDefine } from "../agent/tool/ToolDefine";
import { McpConfig } from "../mcp/McpConfig";
import { McpHub } from "../mcp/McpHub";
// 客户端类，包含客户端地址和任务
export class IdeClient {
    init: boolean = false;
    // 用户账号
    userAccount!: string;
    // 基础URL
    baseUrl!: string;
    // 代理
    agents!: Map<string, AgentDefine>;
    // 工具
    toolDefines!: Map<string, ToolDefine>;
    mcpConfigs!: Map<string, McpConfig>;
    // MCP注册中心
    mcpHub: McpHub;
    // 客户端名称
    clientName: string;
    agentStorge!: AgentStorage;
    // 任务
    tasks: Map<string, Task>;
    // 最后一次心跳时间
    lastHeartBeatTime!: number;
    // 当前客户端所打开项目的目录地址
    projectDir!: string;
    // 客户端类型，是idea还是vscode
    clientType!: string

    constructor(clientName: string) {
        this.clientName = clientName;
        this.tasks = new Map();
        this.agents = new Map();
        this.toolDefines = new Map();
        this.mcpConfigs = new Map();
        this.mcpHub = new McpHub();
    }
    getIdeDir() {
        if (this.clientType === "idea") {
            return path.join(this.projectDir, ".idea", "editCache");
        } else if (this.clientType === "vscode") {
            return path.join(this.projectDir, ".vscode", "editCache");
        } else if (this.clientType === "eclipse") {
            return path.join(this.projectDir, ".metadata", "editCache");
        } else {
            return path.join(this.projectDir, ".agent", "editCache");
        }
    }
    addTask(taskId: string) {
        this.tasks.set(taskId, new Task(taskId));
    }
    cancelTask(taskId: string) {
        const task = this.tasks.get(taskId);
        if (task) {
            task.canceled = true;
        }
        // 如果任务是agent任务，则还需要清理内部子任务
        if (this.agentStorge && this.agentStorge.getTaskId() === taskId) {
            this.agentStorge.clearInnerTask();
        }
    }
    removeTask(taskId: string) {
        this.tasks.delete(taskId);
        // 如果任务是agent任务，则还需要清理内部子任务
        if (this.agentStorge && this.agentStorge.getTaskId() === taskId) {
            this.agentStorge.clearInnerTask();
        }
    }
    addMessage(taskId: string, requestId: string, content: string): void {
        const task = this.tasks.get(taskId);
        if (task) {
            task.messages.set(requestId, content);
        }
    }
    isCanceled(taskId: string): boolean {
        const task = this.tasks.get(taskId);
        if (task) {
            return task.canceled;
        }
        return true;
    }
}

class Task {
    taskId: string;
    messages: Map<string, string>;
    canceled: boolean;

    constructor(taskId: string) {
        this.taskId = taskId;
        this.messages = new Map();
        this.canceled = false;
    }
}
