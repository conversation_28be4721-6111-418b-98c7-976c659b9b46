// 大模型请求消息
// 如果是多模态场景，则content是一个对象，考虑扩展实现
export interface MessageRequest {
    role: string;
    content: string | Content[];
    tool_call_id?: string;
    tool_calls?: ToolCall[];
}
// 多模态时，content是一个对象
export interface Content {
    // type类型包括text、image_url、input_audio等，以大模型实际为准
    type: string;
    text?: string;
    image_url?: Image;
    input_audio?: Audio;
}
export interface Image {
    url: string;
    detail: string;
}
export interface Audio {
    data: string;
    format: string;
}
export interface Tool {
    type: string;
    function: {
        description: string;
        name: string;
        parameters: {
            additionalProperties: boolean;
            properties: {
                [key: string]: {
                    description: string;
                    type: string;
                    items?: {
                        type: string;
                    };
                };
            };
            required: string[];
            type: string;
        };
    };
}
// 大模型返回的模型消息
export interface MessageResponse {
    role: string;
    content: string;
    reasoning_content: string;
    tool_calls: ToolCall[];
    is_finished: boolean;
}
export interface ToolCall {
    id: string;
    type: string;
    index: number;
    function: {
        name: string;
        arguments: string;
    };
}

export interface Choice {
    index: number;
    delta: MessageResponse;
    finish_reason?: string;
}

export interface Result {
    choices?: Choice[];
}