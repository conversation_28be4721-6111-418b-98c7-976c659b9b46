// import { logger } from '../utils/Logger';
// import { OpenAI } from 'openai';

// const client = new OpenAI({
//   apiKey: 'sk-ObzwYW7thm7c6zWjipvzTObfAF5I29-r_YSQ6YDWX96b5YBC',
//   baseURL: 'https://api.morphllm.com/v1'
// });

// export async function applyCodeUpdateOut(url: string, originalCode: string, updateSnippet: string): Promise<string> {
//   try {
//     const response = await client.chat.completions.create({
//       model: "morph-v2",
//       messages: [
//         {
//           role: "user",
//           content: `<code>${originalCode}</code>\n<update>${updateSnippet}</update>`
//         }
//       ],
//       temperature: 0,
//       stream: false
//     });
//     return response.choices[0].message.content || "";
//   } catch (error: unknown) {
//     if (error instanceof Error) {
//       logger.error(`编辑文件工具类执行时，合并文件内容失败：${error.message}`);
//     } else {
//       logger.error('编辑文件工具类执行时，合并文件内容失败：未知错误');
//     }
//     throw error;
//   }
// }

export async function applyCodeUpdate(url: string, originalCode: string, updateSnippet: string): Promise<string> {
  try {
    const response = await fetch(`${url}uis/light-code/uis/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        messages: [
          {
            role: "user",
            content: `<code>${originalCode}</code>\n<update>${updateSnippet}</update>`
          }
        ],
        model: "morph-v2",
        temperature: 0,
        stream: false
      })
    });

    // 检查响应状态
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    const result = await response.json();
    return result.choices[0].message.content || "";
  } catch (error: any) {
    throw error;
  }
}