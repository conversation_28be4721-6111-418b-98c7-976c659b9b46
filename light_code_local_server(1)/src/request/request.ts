import fetch from 'node-fetch';
import Parser from "../agent/parser/AgentModelResponseParser";
import { MessageRequest, Tool } from '../model/LLModel';

export interface AgentChatMessage {
    prompt_type: string;
    model_name: string;
    multimodal: boolean;
    completion?: {
        messages: MessageRequest[];
        tools?: Tool[];
        model: string;
        stream: boolean;
    };
    multimodal_completion?: {
        messages: MessageRequest[];
        tools?: Tool[];
        model: string;
        stream: boolean;
    };
}

export async function request(url: string, data: any): Promise<string> {
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: data
        });

        // 检查响应状态
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.text();
    } catch (error: any) {
        throw error;
    }
}

export async function agentStreamRequest(
    agentChatMessage: AgentChatMessage,
    parser: Parser,
    url: string,
    onMessage: (message: any) => Promise<void>
): Promise<void> {
    try {
        const response = await fetch(`${url}uis/light-code/uis/chat/completions/stream`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(agentChatMessage)
        });

        if (!response.ok) {
            const errorBody = await response.text();
            throw new Error(`大模型请求失败: 状态码 ${response.status}, 错误信息: ${errorBody}`);
        }
        let processPromise = Promise.resolve();
        // 处理流式响应
        const decoder = new TextDecoder('utf-8');
        let buffer = '';
        response.body.on('data', (chunk: Buffer) => {
            // 解码数据块并与缓存拼接
            let text = decoder.decode(chunk, { stream: true });
            if (buffer) {
                text = buffer + text;
                buffer = '';
            }
            // 解析数据块
            let dataBlocks = text
                .split('data:')
                .map((item) => item.trim())
                .filter((item) => item);

            for (const line of dataBlocks) {
                if (!line || line === '[DONE]') {
                    continue;
                };
                let parsed: any;
                try {
                    parsed = JSON.parse(line);
                } catch (e) {
                    buffer = line;
                    continue;
                }
                processPromise = processPromise.then(() => parser.parserResult(parsed, onMessage));
            }
        });

        await new Promise((resolve, reject) => {
            response.body.on('end', resolve);
            response.body.on('error', reject);
        });
    } catch (error: any) {
        throw error;
    }
}