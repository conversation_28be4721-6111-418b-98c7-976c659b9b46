// 消息类型与之前的服务号保持一致
export enum MessageType {
    // 以下是grpc相关消息类型
    ide_to_proc_check_req = 'ide_to_proc_check_req',// 检查进程是否启动请求 
    proc_to_ide_check_resp = 'proc_to_ide_check_resp',// 检查进程是否启动响应
    ide_to_proc_heart_beat_req = 'ide_to_proc_heart_beat_req',// 心跳消息
    proc_to_ide_heart_beat_resp = 'proc_to_ide_heart_beat_resp',// 心跳消息
    // 这里都是异常消息类型
    proc_to_ide_error_inner_server = 'proc_to_ide_error_inner_server',// 进程错误
    proc_to_ide_error_illegal_module_type = 'proc_to_ide_error_illegal_module_type',// 错误的模块类型
    proc_to_ide_error_illegal_message_type = 'proc_to_ide_error_illegal_message_type',// 错误的消息类型
    proc_to_ide_error_illegal_message = 'proc_to_ide_error_illegal_message',// 不符合格式要求的消息
    proc_to_ide_error_client_not_init = 'proc_to_ide_error_client_not_init',// 客户端未初始化

    // 以下是setting相关消息类型
    ide_to_proc_login_req = 'ide_to_proc_login_req', // 登录请求
    proc_to_ide_login_resp = 'proc_to_ide_login_resp', // 登录响应
    ide_to_proc_agent_register_req = 'ide_to_proc_agent_register_req', // 注册agent请求
    proc_to_ide_agent_register_resp = 'proc_to_ide_agent_register_resp', // 注册agent响应
    ide_to_proc_tool_register_req = 'ide_to_proc_tool_register_req', // 注册tool请求
    proc_to_ide_tool_register_resp = 'proc_to_ide_tool_register_resp', // 注册tool响应
    ide_to_proc_mcp_register_req = 'ide_to_proc_mcp_register_req', // 注册MCP请求
    proc_to_ide_mcp_register_resp = 'proc_to_ide_mcp_register_resp', // 注册MCP响应
    ide_to_proc_tools_get_req = 'ide_to_proc_tools_get_req', // 获取工具列表请求
    proc_to_ide_tools_get_resp = 'proc_to_ide_tools_get_resp', // 获取工具列表响应
    ide_to_proc_evn_change_req = 'ide_to_proc_evn_change_req', // 环境变量修改请求
    proc_to_ide_evn_change_resp = 'proc_to_ide_evn_change_resp', // 环境变量修改响应
    ide_to_proc_mcp_tool_get_req = 'ide_to_proc_mcp_tool_get_req', // 获取MCP工具请求
    proc_to_ide_mcp_tool_get_resp = 'proc_to_ide_mcp_tool_get_resp', // 获取MCP工具响应
    ide_to_proc_mcp_check_req = 'ide_to_proc_mcp_check_req', // 检查MCP请求
    proc_to_ide_mcp_check_resp = 'proc_to_ide_mcp_check_resp', // 检查MCP响应

    // 以下是agent相关消息类型
    ide_to_proc_agent_cancel_req = 'ide_to_proc_agent_cancel_req', // 取消agent请求
    proc_to_ide_agent_cancel_resp = 'proc_to_ide_agent_cancel_resp', // 取消agent响应
    ide_to_proc_agent_get_req = 'ide_to_proc_agent_get_req', // 获取agent请求
    ide_to_proc_agent_detail_get_req = 'ide_to_proc_agent_detail_get_req', // 获取agent详情请求
    proc_to_ide_agent_get_resp = 'proc_to_ide_agent_get_resp', // 获取agent响应
    ide_to_proc_agent_stream_req = 'ide_to_proc_agent_stream_req', // 流式发送agent请求
    proc_to_ide_agent_stream_resp = 'proc_to_ide_agent_stream_resp', // 流式发送agent响应
    proc_to_ide_tool_call_req = 'proc_to_ide_tool_call_req', // 调用工具
    ide_to_proc_tool_call_resp = 'ide_to_proc_tool_call_resp', // 调用工具响应
    ide_to_proc_agent_tool_operation_req = 'ide_to_proc_agent_tool_operation_req', // 工具操作请求
    proc_to_ide_agent_tool_operation_resp = 'proc_to_ide_agent_tool_operation_resp', // 工具操作响应
    ide_to_proc_agent_get_file_origin_content_req = 'ide_to_proc_agent_get_file_origin_content_req', // 获取文件原始内容请求
    proc_to_ide_agent_get_file_origin_content_resp = 'proc_to_ide_agent_get_file_origin_content_resp', // 获取文件原始内容响应
    ide_to_proc_agent_refresh_file_origin_content_req = 'ide_to_proc_agent_refresh_file_origin_content_req', // 刷新文件的原始内容请求
    proc_to_ide_agent_refresh_file_origin_content_resp = 'proc_to_ide_agent_refresh_file_origin_content_resp', // 刷新文件的原始内容响应
    ide_to_proc_agent_refresh_diff_count_req = 'ide_to_proc_agent_refresh_diff_count_req', // diff对比数据数量请求
    proc_to_ide_agent_refresh_diff_count_resp = 'proc_to_ide_agent_refresh_diff_count_resp', // diff对比数据数量响应
    ide_to_proc_edit_diff_operator_req = 'ide_to_proc_edit_diff_operator_req', // 编辑diff数据请求
    proc_to_ide_edit_diff_operator_resp = 'proc_to_ide_edit_diff_operator_resp', // 编辑diff数据响应
    ide_to_proc_agent_get_file_origin_path_req = 'ide_to_proc_agent_get_file_origin_path_req', // 获取文件原始路径请求
    proc_to_ide_agent_get_file_origin_path_resp = 'proc_to_ide_agent_get_file_origin_path_resp', // 获取文件原始路径响应
    ide_to_proc_get_agent_conv_list_req = 'ide_to_proc_get_agent_conv_list_req',// 获取agent对话历史列表
    proc_to_ide_get_agent_conv_list_resp = 'proc_to_ide_get_agent_conv_list_resp',// 获取agent对话历史列表
    ide_to_proc_agent_display_message_detail_req = 'ide_to_proc_agent_display_message_detail_req',// 获取agent对话历史详情
    proc_to_ide_agent_display_message_detail_resp = 'proc_to_ide_agent_display_message_detail_resp',// 获取agent对话历史详情
    ide_to_proc_agent_question_detail_req = 'ide_to_proc_agent_question_detail_req',// 获取agent对话问题详情
    proc_to_ide_agent_question_detail_resp = 'proc_to_ide_agent_question_detail_resp',// 获取agent对话问题详情
    ide_to_proc_agent_conv_delete_req = 'ide_to_proc_agent_conv_delete_req',// 删除agent对话历史
    proc_to_ide_agent_conv_delete_resp = 'proc_to_ide_agent_conv_delete_resp',// 删除agent对话历史
    ide_to_proc_agent_token_cost_req = 'ide_to_proc_agent_token_cost_req',// 计算token数量请求
    proc_to_ide_agent_token_cost_resp = 'proc_to_ide_agent_token_cost_resp',// 计算token数量响应

    // 以下是llmModel相关消息类型
    ide_to_proc_token_calc_req = 'ide_to_proc_token_calc_req',// 计算token数量请求
    proc_to_ide_token_calc_resp = 'proc_to_ide_token_calc_resp',// 计算token数量响应
    ide_to_proc_image_token_calc_req = 'ide_to_proc_image_token_calc_req',// 计算图片token数量请求
    proc_to_ide_image_token_calc_resp = 'proc_to_ide_image_token_calc_resp',// 计算图片token数量响应
    ide_to_proc_token_cut_req = 'ide_to_proc_token_cut_req',// 截取token数量请求
    proc_to_ide_token_cut_resp = 'proc_to_ide_token_cut_resp',// 截取token数量响应
    ide_to_proc_token_init_req = 'ide_to_proc_token_init_req',// 初始化token数量请求
    proc_to_ide_token_init_resp = 'proc_to_ide_token_init_resp',// 初始化token数量响应
}

