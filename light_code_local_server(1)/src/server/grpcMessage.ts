export interface GrpcMessage {
    taskId: string;
    requestId: string;
    moduleType: string;
    messageType: string;
    content: string;
    sender: string;
    timestamp: number;
    status: number;
}

export function createMessage(
    taskId: string,
    requestId: string,
    moduleType: string,
    messageType: string,
    content: string,
    status: number = 200,
    sender: string = "Server"
): GrpcMessage {
    return {
        taskId,
        requestId,
        moduleType,
        messageType,
        content,
        sender,
        timestamp: Date.now(),
        status
    };
}