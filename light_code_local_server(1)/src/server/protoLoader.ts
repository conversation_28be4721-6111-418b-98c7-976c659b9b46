// 直接将 proto 文件内容嵌入到代码中
const PROTO_CONTENT = `syntax = "proto3";

package LightCodeGrpc;

// 定义消息服务
service LightCodeGrpcService {
  // 一元调用示例
  rpc sendOneMessage (GrpcMessage) returns (GrpcMessage) {}
  
  // 服务器流式调用示例
  rpc subscribeToMessages (GrpcMessage) returns (stream GrpcMessage) {}
  
  // 客户端流式调用示例
  rpc sendMultipleMessages (stream GrpcMessage) returns (GrpcMessage) {}
  
  // 双向流式调用
  rpc send (stream GrpcMessage) returns (stream GrpcMessage) {}
}

// 定义消息结构
message GrpcMessage {
  string taskId = 1;
  string requestId = 2;
  string moduleType = 3;
  string messageType = 4;
  string content = 5;
  string sender = 6;
  int64 timestamp = 7;
  int32 status = 8;
}`;

export function getProtoContent(): string {
  return PROTO_CONTENT;
} 