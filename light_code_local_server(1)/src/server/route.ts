import { routeSimple as agentSimple } from '../agent/agentRouteSimple';
import { routeStream as agentStream} from '../agent/agentRouteStream';
import { createMessage, GrpcMessage } from './grpcMessage';
import { logger } from '../utils/Logger';
import { routeSimple as channelSimple } from '../channel/channel';
import { routeSimple as settingSimple } from '../setting/setting';
import { routeSimple as llmSimple } from '../llm/llmSimple';
import { MessageType } from './MessageType';
import { ModuleType } from './ModuleType';
import { generateUUID } from '../utils/util';
import { IdeClient } from '../model/IdeClient';
import { GlobalStore, IDE_CLIENT } from '../GlobalStore';
import { CLIENT_NOT_INIT, ILLEGAL_MESSAGE, ILLEGAL_MODULE_TYPE } from '../consts/ErrorCode';

export function routeStream(message: GrpcMessage, call: any) {
    const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(message.sender) as IdeClient;
    // 如果客户端未初始化，则返回错误
    if (!client || !client.init) {
        const errorMessage = createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_error_client_not_init,
            "IDE尚未注册到进程",
            CLIENT_NOT_INIT
        );
        call.write(errorMessage);
        call.end();
        return;
    }
    try {
        if (message.moduleType === "agent") {
            agentStream(message, call);
        } else if (message.moduleType === "completion") {
            // completion.route(message, call);
        } else {
            logger.error(`收到未知模块消息: ${JSON.stringify(message)}`);
            const errorMessage = createMessage(
                message.taskId,
                message.requestId,
                message.moduleType,
                MessageType.proc_to_ide_error_illegal_module_type,
                "错误的功能模块类型：" + message.moduleType,
                ILLEGAL_MODULE_TYPE
            );
            call.write(errorMessage);
            call.end();
        }
    } catch (error) {
        const e = error instanceof Error ? error.message : String(error);
        logger.error(`处理消息失败: ${e}`);
        const errorMessage = createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_error_illegal_message,
            "处理消息失败：" + e,
            ILLEGAL_MESSAGE
        );
        call.write(errorMessage);
        call.end();
    }
}

export function routeSimple(message: GrpcMessage, callback: (error: Error | null, response: any) => void): void {
    try {
        if (message.moduleType === ModuleType.setting) {
            settingSimple(message, callback);
        } else if (message.moduleType === ModuleType.channel) {
            channelSimple(message, callback);
        }  else if (message.moduleType === ModuleType.llm) {
            llmSimple(message, callback);
        } else {
            const client = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(message.sender) as IdeClient;
            // 如果客户端未初始化，则返回错误
            if (!client || !client.init) {
                const errorMessage = createMessage(
                    message.taskId,
                    message.requestId,
                    message.moduleType,
                    MessageType.proc_to_ide_error_client_not_init,
                    "IDE尚未注册到进程",
                    CLIENT_NOT_INIT
                );
                callback(null, errorMessage);
                return;
            }
            if (message.moduleType === ModuleType.completion) {
                // completion.route(message, callback);
            } else if (message.moduleType === ModuleType.agent) {
                agentSimple(message, callback);
            } else {
                logger.info(`收到未知消息: ${JSON.stringify(message)}`);
                const errorMessage = createMessage(
                    message.taskId,
                    generateUUID(),
                    message.moduleType,
                    MessageType.proc_to_ide_error_illegal_module_type,
                    "错误的功能模块类型：" + message.moduleType,
                    ILLEGAL_MODULE_TYPE
                );
                callback(null, errorMessage);
            }
        }
    } catch (error) {
        const e = error instanceof Error ? error.message : String(error);
        logger.error(`处理消息失败: ${e}`);
        const errorMessage = createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_error_illegal_message,
            "处理消息失败：" + e,
            ILLEGAL_MESSAGE
        );
        callback(null, errorMessage);
    }
}