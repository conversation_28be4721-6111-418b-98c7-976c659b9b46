import * as grpc from '@grpc/grpc-js';
import * as protoLoader from '@grpc/proto-loader';
import * as path from 'path';
import * as fs from 'fs';
import { routeStream, routeSimple } from '../server/route';
import { createMessage, GrpcMessage } from './grpcMessage';
import { logger } from '../utils/Logger';
import { getProtoContent } from './protoLoader';
import { DEBUG, GlobalStore, LOG_DIR } from '../GlobalStore';
import { generateUUID } from '../utils/util';
import { MessageType } from './MessageType';
import { ILLEGAL_MESSAGE } from '../consts/ErrorCode';
// 启动服务器
// 在启动服务器之前先打印一些信息
logger.info('正在启动LightCode进程服务...');
const startTime = Date.now();
// 确保在退出前记录错误
process.on('uncaughtException', (error) => {
    logger.error('未捕获的异常:', error);
    // 保持进程运行一段时间以确保日志写入
    setTimeout(() => process.exit(1), 1000);
});

process.on('unhandledRejection', (reason: unknown, promise) => {
    const errorMessage = reason instanceof Error
        ? `错误: ${reason.message}\n堆栈: ${reason.stack}`
        : `错误: ${String(reason)}`;

    logger.error('未处理的 Promise 拒绝', new Error(errorMessage));
});

// 创建 gRPC 服务器
const server = new grpc.Server();

// 处理退出信号
process.on('SIGINT', () => {
    logger.info('收到退出信号，正在关闭服务器...');
    server.tryShutdown(() => {
        logger.info('服务器已关闭');
        process.exit(0);
    });
});

// 创建临时 proto 文件
function createTempProtoFile(): string {
    try {
        // 在当前目录下创建临时目录
        const protoPath = path.join(GlobalStore.getStore().getStaticValue(LOG_DIR), 'LightCodeGrpc.proto');
        fs.writeFileSync(protoPath, getProtoContent());
        return protoPath;
    } catch (error) {
        logger.error('创建临时 proto 文件失败:', error as Error);
        // 保持进程运行一段时间以确保日志写入
        setTimeout(() => process.exit(1), 1000);
        throw error;
    }
}

// 加载 proto 文件
let PROTO_PATH: string;
try {
    PROTO_PATH = createTempProtoFile();
    logger.info(`临时 proto 文件创建成功: ${PROTO_PATH}`);
} catch (error) {
    logger.error('加载 proto 文件失败:', error as Error);
    // 保持进程运行一段时间以确保日志写入
    setTimeout(() => process.exit(1), 1000);
    throw error;
}

let packageDefinition;
try {
    packageDefinition = protoLoader.loadSync(PROTO_PATH, {
        keepCase: true,
        longs: String,
        enums: String,
        defaults: true,
        oneofs: true
    });
    logger.info('proto 文件加载成功');
} catch (error) {
    logger.error('解析 proto 文件失败:', error as Error);
    // 保持进程运行一段时间以确保日志写入
    setTimeout(() => process.exit(1), 1000);
    throw error;
}

// 清理临时文件
try {
    fs.unlinkSync(PROTO_PATH);
    logger.info('临时文件清理成功');
} catch (error) {
    logger.error('清理临时文件失败:', error as Error);
}

let protoDescriptor;
let chatService;
try {
    protoDescriptor = grpc.loadPackageDefinition(packageDefinition) as any;
    chatService = protoDescriptor.LightCodeGrpc;
    logger.info('gRPC 服务定义加载成功');
} catch (error) {
    logger.error('加载 gRPC 服务定义失败:', error as Error);
    // 保持进程运行一段时间以确保日志写入
    setTimeout(() => process.exit(1), 1000);
    throw error;
}

// 实现服务方法
const serviceImplementation = {
    // 一元调用
    sendOneMessage: (
        call: { request: GrpcMessage },
        callback: (error: Error | null, response: any) => void
    ) => {
        const message = call.request;
        logger.debug(`收到一元消息: ${JSON.stringify(message)}`);
        const { taskId, requestId, sender, moduleType, messageType } = message;
        if (taskId && requestId && moduleType && messageType && sender) {
            logger.info(`开始处理任务消息:moduleType=${moduleType},messageType=${messageType}, taskId=${taskId}, requestId=${requestId}, sender=${sender}`);
            // 再路由
            routeSimple(message, callback);
        } else {
            logger.error(`不符合格式要求的消息：${JSON.stringify(message)}`);
            const errorMessage = createMessage(
                taskId,
                generateUUID(),
                message.moduleType,
                MessageType.proc_to_ide_error_illegal_message,
                "消息异常，缺失taskId或requestId",
                ILLEGAL_MESSAGE
            );
            callback(null, errorMessage);
        }
    },

    // 服务器流式调用
    subscribeToMessages: (call: any) => {
        const message = call.request;
        logger.debug(`收到服务器流式消息: ${JSON.stringify(message)}`);
        const { taskId, requestId, sender, moduleType, messageType } = message;
        if (taskId && requestId && moduleType && messageType && sender) {
            logger.info(`开始处理任务消息:moduleType=${moduleType},messageType=${messageType}, taskId=${taskId}, requestId=${requestId}, sender=${sender}`);
            routeStream(message, call);
        } else {
            logger.error(`不符合格式要求的消息：${JSON.stringify(message)}`);
            const errorMessage = createMessage(
                taskId,
                generateUUID(),
                message.moduleType,
                MessageType.proc_to_ide_error_illegal_message,
                "消息异常，缺失taskId或requestId",
                ILLEGAL_MESSAGE
            );
            call.write(errorMessage);
            call.end();
        }

        call.on('cancelled', () => {
        });

        call.on('error', (error: Error) => {
            logger.error('连接错误', error);
        });
    },

    // 客户端流式调用
    clientStreamMessages: (call: any, callback: (error: Error | null, response: any) => void) => {
        const message = call.request;
        const { taskId, requestId, sender, moduleType, messageType } = message;
        let messages: GrpcMessage[] = [];

        call.on('data', (message: GrpcMessage) => {
            logger.debug(`收到客户端流式消息: ${JSON.stringify(message)}`);
            if (taskId && requestId && moduleType && messageType && sender) {
                logger.info(`开始处理任务消息:moduleType=${moduleType},messageType=${messageType}, taskId=${taskId}, requestId=${requestId}, sender=${sender}`);
                messages.push(message);
            } else {
                logger.error(`不符合格式要求的消息：${JSON.stringify(message)}`);
                const errorMessage = createMessage(
                    taskId,
                    generateUUID(),
                    message.moduleType,
                    MessageType.proc_to_ide_error_illegal_message,
                    "消息异常，缺失taskId或requestId",
                    ILLEGAL_MESSAGE
                );
                call.write(errorMessage);
                call.end();
            }
        });

        call.on('end', () => {
            // 添加业务处理，然后返回
            const result = "";
            callback(null,
                createMessage(
                    messages[messages.length - 1]?.taskId,
                    messages[messages.length - 1]?.requestId,
                    messages[messages.length - 1]?.moduleType,
                    messages[messages.length - 1]?.messageType,
                    result
                ));
        });

        call.on('error', (error: Error) => {
            logger.error('客户端流式调用错误:', error);
            callback(error, createMessage(
                messages[messages.length - 1]?.taskId,
                messages[messages.length - 1]?.requestId,
                messages[messages.length - 1]?.moduleType,
                messages[messages.length - 1]?.messageType,
                error.message
            ));
        });
    },

    // 双向流式调用
    send: (call: any) => {
        call.on('data', (message: GrpcMessage) => {
            logger.debug(`收到双向流式消息: ${JSON.stringify(message)}`);
            const { taskId, requestId, sender, moduleType, messageType } = message;
            if (taskId && requestId && moduleType && messageType && sender) {
                logger.info(`开始处理任务消息:moduleType=${moduleType},messageType=${messageType}, taskId=${taskId}, requestId=${requestId}, sender=${sender}`);
                // 再路由
                routeStream(message, call);
            } else {
                logger.error(`不符合格式要求的消息：${JSON.stringify(message)}`);
                const errorMessage = createMessage(
                    taskId,
                    generateUUID(),
                    message.moduleType,
                    MessageType.proc_to_ide_error_illegal_message,
                    "消息异常，缺失taskId或requestId",
                    ILLEGAL_MESSAGE
                );
                call.write(errorMessage);
                call.end();
            }
        });

        call.on('end', () => {
        });

        call.on('error', (error: Error) => {
            logger.error('连接错误', error);
        })
    }
};

server.addService(chatService.LightCodeGrpcService.service, serviceImplementation);


server.bindAsync(
    `127.0.0.1:${GlobalStore.getStore().getStaticValue("port")}`,
    grpc.ServerCredentials.createInsecure(),
    (error: Error | null, bindPort: number) => {
        if (error) {
            logger.error('启动服务失败:', error);
            // 保持进程运行一段时间以确保日志写入
            setTimeout(() => process.exit(1), 1000);
            return;
        }
        logger.info(`LightCode进程服务已启动，监听端口 ${bindPort},耗时${Date.now() - startTime}ms`);
    }
); 