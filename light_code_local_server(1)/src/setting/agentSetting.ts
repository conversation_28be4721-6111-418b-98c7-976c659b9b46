import { AgentDefine, AgentType } from "../agent/AgentDefine";
import { AgentToolDefine } from "../agent/AgentToolDefine";
import { McpToolDefine } from "../agent/McpToolDefine";
import { ToolDefine } from "../agent/tool/ToolDefine";
import { DEFAULT_AGENTS, DEFAULT_TOOL_DEFINES, GlobalStore } from "../GlobalStore";
import { IdeClient } from "../model/IdeClient";
import { Tool } from "../model/LLModel";
import { createMessage, GrpcMessage } from "../server/grpcMessage";
import { MessageType } from "../server/MessageType";
import { getAgent } from "../utils/agentManager";
import { TokenUtils } from "../utils/tokenUtil";
import { getMcpToolDefine, getToolDefine } from "../utils/ToolDefineUtil";
import { tokenTools } from "../utils/toolTransUtil";
interface AgentDefineMessage {
    agents: AgentDefine[];
    models: Model[];
}
interface AgentRegisterResponse {
    customAgents: Map<string, AgentDefine>;
    defaultAgents: Map<string, AgentDefine>;
}
interface Model {
    model: string;
    requestTokenLimit: number;
    wordlistName: string;
}
interface ToolDefineMessage {
    tools: ToolDefine[];
}
interface AgentDetailGetMessage {
    agentId: string;
}
export function toolDefineGet(message: GrpcMessage, ideClient: IdeClient, callback: (error: Error | null, response: any) => void) {
    const tools: ToolDefine[] = [];
    if (ideClient.toolDefines) {
        const toolDefines = Array.from(ideClient.toolDefines.values());
        tools.push(...toolDefines);
    }
    const defaultTools = GlobalStore.getStore().getStaticValue(DEFAULT_TOOL_DEFINES) as Map<string, ToolDefine>;
    if (defaultTools) {
        const dt = Array.from(defaultTools.values());
        tools.push(...dt);
    }
    callback(null, createMessage(
        message.taskId,
        message.requestId,
        message.moduleType,
        MessageType.proc_to_ide_tools_get_resp,
        JSON.stringify(tools)
    ));
}

export function agentListGet(message: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const agentsMap = GlobalStore.getStore().getStaticValue(DEFAULT_AGENTS) as Map<string, AgentDefine>;
    const agents = JSON.parse(JSON.stringify(Array.from(agentsMap.values()))) as AgentDefine[];
    callback(null, createMessage(
        message.taskId,
        message.requestId,
        message.moduleType,
        MessageType.proc_to_ide_agent_get_resp,
        JSON.stringify(agents)
    ));
}

export function agentDetailGet(message: GrpcMessage, ideClient: IdeClient, callback: (error: Error | null, response: any) => void) {
    const { agentId } = JSON.parse(message.content) as AgentDetailGetMessage;
    const agent = getAgent(agentId, ideClient);
    const respAgent = JSON.parse(JSON.stringify(agent));
    respAgent.baseToolDefines?.forEach((toolDefine: AgentToolDefine) => {
        const tool = getToolDefine(toolDefine.name, ideClient);
        toolDefine.tool = tool?.tool;
    });
    respAgent.mcpToolDefines?.forEach((mcpToolDefine: McpToolDefine) => {
        mcpToolDefine.toolDefines.forEach((toolDefine: AgentToolDefine) => {
            const tool = getToolDefine(toolDefine.name, ideClient);
            toolDefine.tool = tool?.tool;
        });
    });
    callback(null, createMessage(
        message.taskId,
        message.requestId,
        message.moduleType,
        MessageType.proc_to_ide_agent_get_resp,
        JSON.stringify(respAgent)
    ));
}

export function toolRegister(message: GrpcMessage, ideClient: IdeClient, callback: (error: Error | null, response: any) => void) {
    const { tools } = JSON.parse(message.content) as ToolDefineMessage;
    tools.forEach((item: ToolDefine) => {
        ideClient.toolDefines.set(item.name, item);
    });
    callback(null, createMessage(
        message.taskId,
        message.requestId,
        message.moduleType,
        MessageType.proc_to_ide_tool_register_resp,
        "success"
    ));
}

export function agentRegister(message: GrpcMessage, ideClient: IdeClient, callback: (error: Error | null, response: any) => void) {
    const { agents, models } = JSON.parse(message.content) as AgentDefineMessage;
    // 删除类型是custom的agent
    // todo:这里不要优化掉。进程中已经删除的agent不要移除缓存，因为对话中还可能会继续使用该agent进行对话
    for (const [id, item] of ideClient.agents.entries()) {
        if (item.type === AgentType.custom) {
            ideClient.agents.delete(id);
        }
    }
    agents.forEach(async (item: AgentDefine) => {
        await Promise.all(models.map(async (model: Model) => {
            let token = await TokenUtils.token(item.systemPrompt, model.wordlistName);
            let tools: Tool[] = [];
            if (item.baseToolDefines) {
                item.baseToolDefines.forEach((tool: AgentToolDefine) => {
                    if (tool.tool) {
                        tools.push(tool.tool);
                    }
                });
            }
            if (item.mcpToolDefines) {
                item.mcpToolDefines.forEach((mcpToolDefine: McpToolDefine) => {
                    const mcpConfig = ideClient.mcpConfigs.get(mcpToolDefine.mcpId);
                    // mcp存在，且未禁用，且连接成功
                    if (mcpConfig && !mcpConfig.disabled && mcpConfig.status === "connected") {
                        mcpToolDefine.toolDefines.forEach((toolDefine) => {
                            const td = getMcpToolDefine(toolDefine.name, ideClient);
                            if (td) {
                                tools.push(td.tool);
                            }
                        });
                    }
                });
            }
            token += await tokenTools(tools, model.model, model.wordlistName);
            // 拍脑袋加的，因为agent模式下，还会有一些其它的插槽，所以需要加一个预留
            token += 1000;
            if (item.tokenCosts) {
                item.tokenCosts.push({
                    model: model.model,
                    wordlistName: model.wordlistName,
                    tokens: token
                });
            } else {
                item.tokenCosts = [{
                    model: model.model,
                    wordlistName: model.wordlistName,
                    tokens: token
                }];
            }
        }));
        ideClient.agents.set(item.id, item);
    });
    // 同时根据模型计算内置Agent的token消耗
    const defaultAgents = GlobalStore.getStore().getStaticValue(DEFAULT_AGENTS) as Map<string, AgentDefine>;
    defaultAgents.forEach((agent: AgentDefine) => {
        models.forEach(async (model: Model) => {
            let token = await TokenUtils.token(agent.systemPrompt, model.wordlistName);
            let tools: Tool[] = [];
            if (agent.baseToolDefines) {
                agent.baseToolDefines.forEach((tool: AgentToolDefine) => {
                    if (tool.tool) {
                        tools.push(tool.tool);
                    }
                });
            }
            if (agent.mcpToolDefines) {
                agent.mcpToolDefines.forEach((mcpToolDefine: McpToolDefine) => {
                    mcpToolDefine.toolDefines.forEach((tool: AgentToolDefine) => {
                        if (tool.tool) {
                            tools.push(tool.tool);
                        }
                    });
                });
            }
            token += await tokenTools(tools, model.model, model.wordlistName);
            // 拍脑袋加的，因为agent模式下，还会有一些其它的插槽，所以需要加一个预留
            token += 1000;
            if (agent.tokenCosts) {
                agent.tokenCosts.push({
                    model: model.model,
                    wordlistName: model.wordlistName,
                    tokens: token
                });
            } else {
                agent.tokenCosts = [{
                    model: model.model,
                    wordlistName: model.wordlistName,
                    tokens: token
                }];
            }
        });
    });
    callback(null, createMessage(
        message.taskId,
        message.requestId,
        message.moduleType,
        MessageType.proc_to_ide_agent_register_resp,
        "success"
    ));
}