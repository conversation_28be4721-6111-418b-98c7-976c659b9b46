import { AgentDefine, AgentType } from "../agent/AgentDefine";
import { IdeClient } from "../model/IdeClient";
import { createMessage, GrpcMessage } from "../server/grpcMessage";
import { MessageType } from "../server/MessageType";
import { DEFAULT_TOOL_DEFINES, GlobalStore, IDE_CLIENT } from "../GlobalStore";
import { McpConfig } from "../mcp/McpConfig";
import { loadMcp } from "../mcp/mcp";
import { ToolDefine } from "../agent/tool/ToolDefine";
import { logger } from "../utils/Logger";

interface UserInfoMessage {
    baseUrl: string;
    userAccount: string;
    agents: AgentDefine[];
    tools: ToolDefine[];
    mcpConfigs: McpConfig[];
    projectDir: string;
    clientType: string
}
export function ideRegistry(message: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    const { baseUrl, userAccount, agents, tools, mcpConfigs, projectDir, clientType } = JSON.parse(message.content) as UserInfoMessage;
    let ideClient = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(message.sender) as IdeClient;
    if (!ideClient) {
        ideClient = new IdeClient(message.sender);
        GlobalStore.getStore().getStaticValue(IDE_CLIENT).set(message.sender, ideClient);
    }
    ideClient.init = true;
    ideClient.userAccount = userAccount;
    ideClient.baseUrl = baseUrl;
    ideClient.projectDir = projectDir;
    ideClient.clientType = clientType;

    // 删除类型是custom的agent
    for (const [id, item] of ideClient.agents.entries()) {
        if (item.type === AgentType.custom) {
            ideClient.agents.delete(id);
        }
    }
    // 注册tool
    tools?.forEach((item: ToolDefine) => {
        ideClient.toolDefines.set(item.name, item);
    });
    // 注册agent
    agents?.forEach((item: AgentDefine) => {
        ideClient.agents.set(item.id, item);
    });
    mcpConfigs?.forEach((item: McpConfig) => {
        ideClient.mcpConfigs.set(item.id, item);
    });
    // 注意这里是异步的，但是这里并不await
    loadMcp(ideClient).catch((error) => {
        logger.error(`加载MCP失败，原因: ${error}`);
    });

    // 数据回发，将进程自动加载的一些数据回发给IDE
    const defaultTools = GlobalStore.getStore().getStaticValue(DEFAULT_TOOL_DEFINES) as Map<string, ToolDefine>;
    const dt = Array.from(defaultTools.values());
    if (tools) {
        dt.push(...tools);
    }
    callback(null, createMessage(
        message.taskId,
        message.requestId,
        message.moduleType,
        MessageType.proc_to_ide_login_resp,
        JSON.stringify({
            tools: dt
        })
    ));
}

export function evnChange(message: GrpcMessage, ideClient: IdeClient, callback: (error: Error | null, response: any) => void) {
    const { baseUrl, userAccount } = JSON.parse(message.content) as UserInfoMessage;
    ideClient.baseUrl = baseUrl;
    ideClient.userAccount = userAccount;
    callback(null, createMessage(
        message.taskId,
        message.requestId,
        message.moduleType,
        MessageType.proc_to_ide_evn_change_resp,
        "success"
    ));
}
