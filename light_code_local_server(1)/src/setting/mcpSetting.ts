import { loadMcp } from "../mcp/mcp";
import { IdeClient } from "../model/IdeClient";
import { createMessage, GrpcMessage } from "../server/grpcMessage";
import { McpConfig } from "../mcp/McpConfig";
import { MessageType } from "../server/MessageType";
import { MCP_LOAD_ERROR } from "../consts/ErrorCode";

interface McpConfigsMessage {
    mcpConfigs: McpConfig[];
}
interface McpConfigMessage {
    mcpConfig: McpConfig;
}

export function mcpRegister(message: GrpcMessage, ideClient: IdeClient, callback: (error: Error | null, response: any) => void) {
    const { mcpConfigs } = JSON.parse(message.content) as McpConfigsMessage;
    ideClient.mcpConfigs.clear();
    mcpConfigs.forEach((item: McpConfig) => {
        ideClient.mcpConfigs.set(item.id, item);
    });
    loadMcp(ideClient).then(() => {
        callback(null, createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_mcp_register_resp,
            JSON.stringify(Array.from(ideClient.mcpConfigs.values()))
        ));
    });
}

export function mcpToolGet(message: GrpcMessage, ideClient: IdeClient, callback: (error: Error | null, response: any) => void) {
    const { mcpConfig } = JSON.parse(message.content) as McpConfigMessage;
    const connection = ideClient.mcpHub.connections.get(mcpConfig.id);
    if (connection) {
        const toolDefines = connection.server.toolDefines ?? [];
        callback(null, createMessage(
            message.taskId,
            message.requestId,
            message.moduleType,
            MessageType.proc_to_ide_mcp_tool_get_resp,
            JSON.stringify(toolDefines)
        ));
    } else {
        ideClient.mcpHub.connectToServer(mcpConfig)
            .then((connection) => {
                return ideClient.mcpHub.loadData(connection, mcpConfig)
                    .then((toolDefines) => {
                        callback(null, createMessage(
                            message.taskId,
                            message.requestId,
                            message.moduleType,
                            MessageType.proc_to_ide_mcp_tool_get_resp,
                            JSON.stringify(toolDefines)
                        ));
                    });
            })
            .catch((error) => {
                const errorMessage = createMessage(
                    message.taskId,
                    message.requestId,
                    message.moduleType,
                    MessageType.proc_to_ide_mcp_tool_get_resp,
                    `Mcp服务连接失败,${error}`,
                    MCP_LOAD_ERROR
                );
                callback(null, errorMessage);
            });
    }
}

export function mcpCheck(message: GrpcMessage, ideClient: IdeClient, callback: (error: Error | null, response: any) => void) {
    const { mcpConfig } = JSON.parse(message.content) as McpConfigMessage;
    ideClient.mcpHub.connectToServer(mcpConfig)
        .then(() => {
            callback(null, createMessage(
                message.taskId,
                message.requestId,
                message.moduleType,
                MessageType.proc_to_ide_mcp_check_resp,
                "Mcp服务连接成功"
            ));
        })
        .catch((error) => {
            const errorMessage = createMessage(
                message.taskId,
                message.requestId,
                message.moduleType,
                MessageType.proc_to_ide_mcp_check_resp,
                `Mcp服务连接失败,${error}`,
                MCP_LOAD_ERROR
            );
            callback(null, errorMessage);
        });
}

// function assembleToolDefines(toolDefines: ToolDefine[] | undefined, callback: (error: Error | null, response: any) => void, message: GrpcMessage) {
//     const toolDefineInfos: ToolDefine[] = [];
//     if (toolDefines) {
//         toolDefines.forEach((toolDefine: ToolDefine) => {
//             const tool = new ToolDefine(toolDefine.name, toolDefine.source, toolDefine.autoExec, toolDefine.displayName);
//             tool.description = toolDefine.tool?.function.description;
//             toolDefineInfos.push(tool);
//         });
//     }
//     callback(null, createMessage(
//         message.taskId,
//         message.requestId,
//         message.moduleType,
//         MessageType.proc_to_ide_mcp_tool_get_resp,
//         JSON.stringify(toolDefineInfos)
//     ));
// }

