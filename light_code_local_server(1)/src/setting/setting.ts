import { createMessage, GrpcMessage } from "../server/grpcMessage";
import { MessageType } from "../server/MessageType";
import { GlobalStore, IDE_CLIENT } from "../GlobalStore";
import { logger } from "../utils/Logger";
import { IdeClient } from "../model/IdeClient";
import { CLIENT_NOT_INIT, ILLEGAL_MESSAGE_TYPE } from "../consts/ErrorCode";
import { agentDetailGet, agentListGet, agentRegister, toolDefineGet } from "./agentSetting";
import { toolRegister } from "./agentSetting";
import { mcpToolGet, mcpRegister, mcpCheck } from "./mcpSetting";
import { evnChange, ideRegistry } from "./ideRegister";


export function routeSimple(message: GrpcMessage, callback: (error: Error | null, response: any) => void) {
    // 登录注册请求
    // 只有在收到注册请求时，才会缓存IDE客户端
    if (message.messageType === MessageType.ide_to_proc_login_req) {
        ideRegistry(message, callback);
    } else {
        const ideClient = GlobalStore.getStore().getStaticValue(IDE_CLIENT).get(message.sender) as IdeClient;
        // 如果客户端未初始化，则返回错误
        if (!ideClient || !ideClient.init) {
            const errorMessage = createMessage(
                message.taskId,
                message.requestId,
                message.moduleType,
                MessageType.proc_to_ide_error_client_not_init,
                "IDE尚未注册到进程",
                CLIENT_NOT_INIT
            );
            callback(null, errorMessage);
            return;
        }
        if (message.messageType === MessageType.ide_to_proc_agent_register_req) {
            // 注册用户自定义agent请求
            agentRegister(message, ideClient, callback);
        } else if (message.messageType === MessageType.ide_to_proc_tool_register_req) {
            // 注册tool请求
            toolRegister(message, ideClient, callback);
        } else if (message.messageType === MessageType.ide_to_proc_evn_change_req) {
            // 服务器环境地址修改请求
            evnChange(message, ideClient, callback);
        } else if (message.messageType === MessageType.ide_to_proc_mcp_register_req) {
            // 处理MCP配置注册请求
            mcpRegister(message, ideClient, callback);
        } else if (message.messageType === MessageType.ide_to_proc_mcp_tool_get_req) {
            // 获取MCP工具请求
            mcpToolGet(message, ideClient, callback);
        } else if (message.messageType === MessageType.ide_to_proc_mcp_check_req) {
            // MCP服务检查请求
            mcpCheck(message, ideClient, callback);
        } else if (message.messageType === MessageType.ide_to_proc_tools_get_req) {
            toolDefineGet(message, ideClient, callback);
        } else if (message.messageType === MessageType.ide_to_proc_agent_get_req) {
            agentListGet(message, callback);
        } else if (message.messageType === MessageType.ide_to_proc_agent_detail_get_req) {
            agentDetailGet(message, ideClient, callback);
        } else {
            logger.error(`收到未知setting处理消息: ${JSON.stringify(message)}`);
            const errorMessage = createMessage(
                message.taskId,
                message.requestId,
                message.moduleType,
                MessageType.proc_to_ide_error_illegal_message_type,
                "未知的消息类型。功能模块：" + message.moduleType + "，消息类型：" + message.messageType,
                ILLEGAL_MESSAGE_TYPE
            );
            callback(null, errorMessage);
        }
    }
}
