import { HEART_BEAT_INTERVAL, GlobalStore, IDE_CLIENT } from "../GlobalStore";
import { IdeClient } from "../model/IdeClient";
import { logger } from "../utils/Logger";

/**
 * 客户端存活检测
 * 每30秒检测一次，如果30秒内没有收到心跳，则认为客户端已断开连接
 */
function clientCheck() {
    logger.info(`开启进程自检任务`);
    // 添加一个自旋检测，每30秒检测一次
    // 如果30秒内没有收到心跳，则认为客户端已断开连接
    setInterval(() => {
        const ideClientMap = GlobalStore.getStore().getStaticValue(IDE_CLIENT) as Map<string, IdeClient>;
        if (ideClientMap) {
            for (const [clientName, ideClient] of ideClientMap.entries()) {
                if (ideClient.lastHeartBeatTime && Date.now() - ideClient.lastHeartBeatTime > 30000) {
                    logger.info(`IDE客户端${ideClient.clientName}超过30秒未心跳，清理IDE客户端`);
                    ideClientMap.delete(clientName);
                }
            }
        }
        // 如果IDE客户端已全部断开连接且超过30秒，则关闭进程
        if (ideClientMap.size == 0) {
            logger.info(`IDE客户端已全部断开连接且超过30秒，关闭进程`);
            // 关闭进程，等待日志输出完毕之后
            setTimeout(() => {
                process.exit(0);
            }, 1000);
        }
    }, GlobalStore.getStore().getStaticValue(HEART_BEAT_INTERVAL) * 3);
}

clientCheck();

