import * as fs from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import { GlobalStore, DEBUG } from '../GlobalStore';

const appendFile = promisify(fs.appendFile);
const writeFile = promisify(fs.writeFile);
const rename = promisify(fs.rename);
const unlink = promisify(fs.unlink);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

class Logger {
    private logDir: string;
    private logFile: string;
    private errorFile: string;
    private debugFile: string;
    private initialized: boolean = false;
    private readonly MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    private writeQueue: Map<string, string[]> = new Map();
    private isProcessing: Map<string, boolean> = new Map();

    constructor() {
        // 构造函数中不再立即初始化
        this.logDir = '';
        this.logFile = '';
        this.errorFile = '';
        this.debugFile = '';
    }

    init(baseDir: string) {
        if (this.initialized) {
            return;
        }

        this.logDir = path.join(baseDir, '/logs');
        if (!fs.existsSync(this.logDir)) {
            try {
                fs.mkdirSync(this.logDir, { recursive: true });
            } catch (error) {
                process.stderr.write(`创建日志目录失败: ${error}\n`);
                this.logDir = baseDir;
            }
        }

        this.logFile = path.join(this.logDir, 'lightcode.log');
        this.errorFile = path.join(this.logDir, 'error.log');
        this.debugFile = path.join(this.logDir, 'debug.log');

        try {
            fs.appendFileSync(this.logFile, '');
            fs.appendFileSync(this.errorFile, '');
        } catch (error) {
            process.stderr.write(`无法写入日志文件: ${error}\n`);
            this.writeToFile = this.writeToConsole;
        }

        this.initialized = true;
    }

    private formatMessage(level: string, message: string): string {
        // 创建一个新的Date对象
        const date = new Date();
        // 转换为北京时间 (UTC+8)
        const beijingTime = new Date(date.getTime() + (8 * 60 * 60 * 1000));
        // 格式化为 YYYY-MM-DD HH:mm:ss 格式
        const timestamp = beijingTime.toISOString()
            .replace('T', ' ')
            .replace(/\.\d+Z$/, '');

        // 获取调用栈信息
        const stack = new Error().stack;
        const callerLine = stack?.split('\n')[3]?.trim() || '位置未知';

        return `[${timestamp}] [${level}] [${callerLine}] ${message}\n`;
    }

    private async getFileSize(filePath: string): Promise<number> {
        try {
            if (fs.existsSync(filePath)) {
                const stats = await stat(filePath);
                return stats.size;
            }
            return 0;
        } catch (error) {
            process.stderr.write(`获取文件大小失败: ${error}\n`);
            return 0;
        }
    }

    private async rotateFile(filePath: string) {
        try {
            const fileSize = await this.getFileSize(filePath);
            if (fileSize >= this.MAX_FILE_SIZE) {
                const dir = path.dirname(filePath);
                const ext = path.extname(filePath);
                const baseName = path.basename(filePath, ext);

                // 获取当前日期
                const now = new Date();
                const dateStr = now.toISOString().split('T')[0]; // YYYY-MM-DD

                // 获取当前日期的所有日志文件
                const files = await readdir(dir);
                const dateFiles = files
                    .filter(f => f.startsWith(`${baseName}.${dateStr}`))
                    .sort();

                // 计算下一个序号
                let nextIndex = 1;
                if (dateFiles.length > 0) {
                    const lastFile = dateFiles[dateFiles.length - 1];
                    const match = lastFile.match(/\.(\d+)$/);
                    if (match) {
                        nextIndex = parseInt(match[1]) + 1;
                    }
                }

                // 生成新的文件名
                const newFileName = `${baseName}.${dateStr}.${nextIndex}${ext}`;
                const newFilePath = path.join(dir, newFileName);

                await rename(filePath, newFilePath);
                await writeFile(filePath, '');

                // 清理旧日志文件，只保留最近5个
                const oldFiles = files
                    .filter(f => f.startsWith(baseName) && f !== path.basename(filePath));

                // 获取所有文件的状态并排序
                const fileStats = await Promise.all(
                    oldFiles.map(async (file) => {
                        const stats = await stat(path.join(dir, file));
                        return { file, mtime: stats.mtime.getTime() };
                    })
                );

                const sortedFiles = fileStats
                    .sort((a, b) => b.mtime - a.mtime)
                    .map(item => item.file);

                if (sortedFiles.length > 5) {
                    for (let i = 5; i < sortedFiles.length; i++) {
                        await unlink(path.join(dir, sortedFiles[i]));
                    }
                }
            }
        } catch (error) {
            process.stderr.write(`日志文件轮转失败: ${error}\n`);
        }
    }

    private async processWriteQueue(filePath: string) {
        if (this.isProcessing.get(filePath)) return;
        this.isProcessing.set(filePath, true);

        try {
            const messages = this.writeQueue.get(filePath) || [];
            if (messages.length === 0) {
                this.isProcessing.set(filePath, false);
                return;
            }

            const message = messages.shift();
            if (message) {
                await this.rotateFile(filePath);
                await appendFile(filePath, message);
            }

            this.writeQueue.set(filePath, messages);
            this.isProcessing.set(filePath, false);

            if (messages.length > 0) {
                setImmediate(() => this.processWriteQueue(filePath));
            }
        } catch (error) {
            process.stderr.write(`写入日志失败: ${error}\n`);
            this.isProcessing.set(filePath, false);
        }
    }

    private writeToFile(filePath: string, message: string) {
        if (!this.writeQueue.has(filePath)) {
            this.writeQueue.set(filePath, []);
        }
        this.writeQueue.get(filePath)?.push(message);
        this.processWriteQueue(filePath);
    }

    private writeToConsole(_filePath: string, message: string) {
        process.stdout.write(message);
    }

    debug(message: string | object) {
        if (GlobalStore.getStore().getStaticValue(DEBUG)) {
            const formattedMessage = typeof message === 'string' ? message : JSON.stringify(message, null, 2);
            const logMessage = this.formatMessage('DEBUG', formattedMessage);
            this.writeToFile(this.debugFile, logMessage);
            this.writeToFile(this.logFile, logMessage);
        }
    }

    info(message: string | object) {
        const formattedMessage = typeof message === 'string' ? message : JSON.stringify(message, null, 2);
        const logMessage = this.formatMessage('INFO', formattedMessage);
        this.writeToFile(this.logFile, logMessage);
    }

    error(message: string | object, error?: Error) {
        const formattedMessage = typeof message === 'string' ? message : JSON.stringify(message, null, 2);
        let errorMessage = this.formatMessage('ERROR', formattedMessage);
        if (error) {
            errorMessage += this.formatMessage('ERROR', `Stack: ${error.stack}\n`);
        }
        this.writeToFile(this.errorFile, errorMessage);
        this.writeToFile(this.logFile, errorMessage);
    }

    warn(message: string | object) {
        const formattedMessage = typeof message === 'string' ? message : JSON.stringify(message, null, 2);
        const logMessage = this.formatMessage('WARN', formattedMessage);
        this.writeToFile(this.logFile, logMessage);
    }

    getLogDir(): string {
        return this.logDir;
    }

    getLogFile(): string {
        return this.logFile;
    }

    getErrorFile(): string {
        return this.errorFile;
    }

    getDebugFile(): string {
        return this.debugFile;
    }

    updateLogDir(newLogDir: string) {
        this.logDir = path.join(newLogDir, '.lightcode/logs');
        if (!fs.existsSync(this.logDir)) {
            try {
                fs.mkdirSync(this.logDir, { recursive: true });
            } catch (error) {
                process.stderr.write(`创建日志目录失败: ${error}\n`);
                this.logDir = newLogDir;
            }
        }

        // 更新日志文件路径
        this.logFile = path.join(this.logDir, 'lightcode.log');
        this.errorFile = path.join(this.logDir, 'error.log');
        this.debugFile = path.join(this.logDir, 'debug.log');

        // 测试是否可以写入日志
        try {
            fs.appendFileSync(this.logFile, '');
            fs.appendFileSync(this.errorFile, '');
            fs.appendFileSync(this.debugFile, '');
        } catch (error) {
            process.stderr.write(`无法写入日志文件: ${error}\n`);
            this.writeToFile = this.writeToConsole;
        }
    }
}

// 导出未初始化的logger实例
export const logger = new Logger(); 