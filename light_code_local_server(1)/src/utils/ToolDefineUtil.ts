import { DEFAULT_TOOL_DEFINES, GlobalStore } from "../GlobalStore";
import { IdeClient } from "../model/IdeClient";
import { ToolDefine } from "../agent/tool/ToolDefine";

export function getToolDefine(toolName: string, client: IdeClient): ToolDefine | undefined {
    let toolDefine: ToolDefine | undefined;
    // 优先从客户端获取
    toolDefine = getBaseToolDefine(toolName, client);
    if (!toolDefine) {
        // 如果默认工具定义中没有，则从MCP获取
        toolDefine = getMcpToolDefine(toolName, client);
    }
    return toolDefine;
}

export function getMcpToolDefine(toolName: string, client: IdeClient): ToolDefine | undefined {
    const mcpConnection = client.mcpHub.toolToMcpServer.get(toolName);
    if (mcpConnection) {
        return mcpConnection.server.toolDefines?.find((tool) => tool.name === toolName);
    }
    return undefined;
}

export function getBaseToolDefine(toolName: string, client: IdeClient): ToolDefine | undefined {
    let toolDefine: ToolDefine | undefined;
    toolDefine = client.toolDefines.get(toolName);
    if (!toolDefine) {
        // 如果客户端没有，则从默认工具定义中获取
        toolDefine = GlobalStore.getStore().getStaticValue(DEFAULT_TOOL_DEFINES).get(toolName);
    }
    return toolDefine;
}