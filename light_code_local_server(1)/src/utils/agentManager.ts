import { DEFAULT_AGENTS, GlobalStore } from "../GlobalStore";
import { Tool } from "../model/LLModel";
import { IdeClient } from "../model/IdeClient";
import { AgentDefine } from "../agent/AgentDefine";
import { AgentToolDefine } from "../agent/AgentToolDefine";
import { getBaseToolDefine, getMcpToolDefine } from "./ToolDefineUtil";
import { TokenUtils } from "./tokenUtil";

/**
 * 获取agent
 * @param agentId 
 * @param client 
 * @returns 
 */
export function getAgent(agentId: string, client: IdeClient): AgentDefine {
    let agent;
    // 优先从客户端缓存中获取
    agent = client.agents.get(agentId);
    // 如果客户端缓存中没有，则从全局缓存中获取
    if (!agent) {
        agent = GlobalStore.getStore().getStaticValue(DEFAULT_AGENTS).get(agentId) as AgentDefine;
    }
    if (!agent) {
        throw new Error(`Agent ${agentId} not found`);
    }
    return agent;
}
export async function getTokenCost(agentId: string, model: string, wordlistName: string, client: IdeClient): Promise<number> {
    const agent = getAgent(agentId, client);
    if (agent.tokenCosts) {
        const tokenCost = agent.tokenCosts.find(item => item.model === model && item.wordlistName === wordlistName);
        if (tokenCost) {
            return tokenCost.tokens;
        }
    } else {
        agent.tokenCosts = [];
    }
    const tokenCost = await TokenUtils.token(agent.systemPrompt, wordlistName);
    agent.tokenCosts.push({
        model: model,
        wordlistName: wordlistName,
        tokens: tokenCost
    });
    return tokenCost;
}
/**
 * 获取工具列表
 * 这里获取的是传递给大模型的工具定义
 * @param agentId 
 * @returns 
 */
export function getAgentTools(agentId: string, client: IdeClient): Tool[] {
    const agent = getAgent(agentId, client);
    const tools: Tool[] = [];
    agent.baseToolDefines?.forEach((toolDefine) => {
        const td = getBaseToolDefine(toolDefine.name, client);
        if (td) {
            tools.push(td.tool);
        }
    });
    agent.mcpToolDefines?.forEach((mcpToolDefine) => {
        const mcpConfig = client.mcpConfigs.get(mcpToolDefine.mcpId);
        // mcp存在，且未禁用，且连接成功
        if (mcpConfig && !mcpConfig.disabled && mcpConfig.status === "connected") {
            mcpToolDefine.toolDefines.forEach((toolDefine) => {
                const td = getMcpToolDefine(toolDefine.name, client);
                if (td) {
                    tools.push(td.tool);
                }
            });
        }
    });
    return tools;
}

/**
 * 获取Agent的工具
 * @param agentId 
 * @param toolName 
 * @param client 
 * @returns 
 */
export function getAgentToolDefine(agentId: string, toolName: string, client: IdeClient): AgentToolDefine | undefined {
    const agent = getAgent(agentId, client);
    let toolDefine = agent.baseToolDefines?.find((toolDefine) => toolDefine.name === toolName);
    if (toolDefine) {
        return toolDefine;
    }
    for (const mcpToolDefine of agent.mcpToolDefines ?? []) {
        for (const toolDefine of mcpToolDefine.toolDefines) {
            if (toolDefine.name === toolName) {
                return toolDefine;
            }
        }
    }
    return undefined;
}

/**
 * 获取agent的系统prompt
 * @param agentId 
 * @param client 
 * @returns 
 */
export function getAgentSystemPrompt(agentId: string, client: IdeClient) {
    const agent = getAgent(agentId, client);
    return agent.systemPrompt;
}
