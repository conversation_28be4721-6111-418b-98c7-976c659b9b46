import { IdeClient } from '../model/IdeClient';

/**
 * 异步等待响应,有超时限制
 * @param client 
 * @param taskId 
 * @param requestId 
 * @param timeout 
 * @returns 
 */
export async function waitForResponse(
    client: IdeClient,
    taskId: string,
    requestId: string,
    timeout: number = 30000
): Promise<string | null> {
    return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
            const task = client.tasks.get(taskId);
            if (task) {
                const response = task.messages.get(requestId);
                if (response) {
                    clearInterval(checkInterval);
                    resolve(response);
                    task.messages.delete(requestId);
                }
            } else {
                clearInterval(checkInterval);
                resolve("stop");
            }
        }, 10);
        if (timeout <= 0) {
            timeout = 30000;
        }
        setTimeout(() => {
            clearInterval(checkInterval);
            resolve(null);
            client.tasks.get(taskId)?.messages.delete(requestId);
        }, timeout);
    });
}

/**
 * 异步等待响应，无超时限制
 * 因为是没有超时限制的，所以需要手动取消，否则会导致内存泄漏
 * @param client 
 * @param taskId 
 * @param requestId 
 * @returns 返回一个包含Promise和cancel函数的对象
 */
export function waitForCancelResponse(
    client: IdeClient,
    taskId: string,
    requestId: string,
): { promise: Promise<string>; cancel: () => void } {
    let checkInterval: NodeJS.Timeout;

    const promise = new Promise<string>((resolve) => {
        checkInterval = setInterval(() => {
            const task = client.tasks.get(taskId);
            if (task && !task.canceled) {
                const response = task.messages.get(requestId);
                if (response) {
                    clearInterval(checkInterval);
                    resolve(response);
                    task.messages.delete(requestId);
                }
            } else {
                clearInterval(checkInterval);
                resolve("cancel");
            }
        }, 10);
    });

    const cancel = () => {
        clearInterval(checkInterval);
        client.tasks.get(taskId)?.messages.delete(requestId);
    };

    return { promise, cancel };
}