import { Tokenizer } from '@anush008/tokenizers';
import * as fs from 'fs';
import { imageSize } from 'image-size';
import * as path from 'path';
import o200k from 'tiktoken/encoders/o200k_base';
import { Tiktoken } from 'tiktoken/lite';
import { AgentModelMessage } from '../agent/AgentStorage';
import { logger } from './Logger';
export class TokenUtils {

  // 分词器缓存,key是词库文件名
  private static tokenizerMap: Map<string, Tokenizer | Tiktoken> = new Map();

  /**
   * 计算token数量
   * 如果模型不存在，使用兜底gpt-4o
   * @param content 内容
   * @param wordlistName 词库名称
   * @returns token数量
   */
  static async token(content: string, wordlistName: string): Promise<number> {
    if (content === '') {
      return 0;
    }
    let tokenizer = TokenUtils.tokenizerMap.get(wordlistName);
    if (!tokenizer) {
      tokenizer = this.loadWordList(wordlistName);
      if (tokenizer == null) {
        logger.error('暂不支持的词库：' + wordlistName);
        return content.length;
      }
    }
    if (tokenizer instanceof Tiktoken) {
      return tokenizer.encode(content).length;
    } else {
      const wpEncoded = await tokenizer.encode(content);
      return wpEncoded.getLength();
    }
  }

  static loadWordList(wordlistName: string): Tokenizer | Tiktoken | undefined {
    // 对于gpt的模型，因为不是走的Tokenizer实现，所以需要单独处理
    if (wordlistName === 'o200k_base') {
      logger.info('加载词库:o200k_base');
      const tiktoken = new Tiktoken(o200k.bpe_ranks, o200k.special_tokens, o200k.pat_str);
      TokenUtils.tokenizerMap.set('o200k_base', tiktoken);
      logger.info('加载词库:o200k_base成功');
      return tiktoken;
    } else {
      return this.loadByTokenizer(wordlistName);
    }
  }

  /**
   * 计算token数量，并返回内容和剩余token数量
   * @param content 内容
   * @param wordlistName 词库名称
   * @param maxTokens 最大token数量
   * @returns 内容和剩余token数量
   */
  static async tokensCut(content: string, wordlistName: string, maxTokens: number): Promise<any> {
    if (content === '') {
      return content;
    }
    let tokenizer = TokenUtils.tokenizerMap.get(wordlistName);
    if (!tokenizer) {
      tokenizer = this.loadWordList(wordlistName);
      if (tokenizer == null) {
        logger.error('暂不支持的词库：' + wordlistName);
        return { content: content, leftTokens: maxTokens - content.length };
      }
    }
    // gpt相关模型
    if (tokenizer instanceof Tiktoken) {
      let tokens = tokenizer.encode(content);
      let leftTokens = maxTokens - tokens.length;
      if (tokens != null && tokens.length > maxTokens) {
        tokens = tokens.slice(0, maxTokens);
        leftTokens = 0;
      }
      const de: Uint8Array = tokenizer.decode(tokens);
      const decoder = new TextDecoder('utf-8');
      return { content: decoder.decode(de), leftTokens: leftTokens };
    } else {
      // 其他模型
      const wpEncoded = await tokenizer.encode(content);
      let tokens = wpEncoded.getIds();
      let leftTokens = maxTokens - tokens.length;
      if (tokens != null && tokens.length > maxTokens) {
        tokens = tokens.slice(0, maxTokens);
        leftTokens = 0;
      }
      return { content: await tokenizer.decode(tokens, false), leftTokens: leftTokens };
    }
  }

  /**
   * 获取分词器
   * @param wordlistName 模型词库
   * @returns 分词器
   */
  static loadByTokenizer(wordlistName: string): Tokenizer | undefined {
    if (!wordlistName) {
      logger.error(`词库文件未配置`);
      return undefined;
    }
    logger.info('加载词库:' + wordlistName);

    // 获取资源文件的基础路径
    let basePath: string;

    // 首先尝试从生产环境路径获取
    const execDir = path.dirname(process.execPath);
    const prodResourcesPath = path.join(execDir, 'resources');

    // 然后尝试从开发环境路径获取
    const devResourcesPath = path.resolve(__dirname, '../../resources');

    // 检查哪个路径存在
    if (fs.existsSync(prodResourcesPath)) {
      basePath = prodResourcesPath;
    } else if (fs.existsSync(devResourcesPath)) {
      basePath = devResourcesPath;
    } else {
      logger.error('无法找到资源目录，请确保 resources 目录存在');
      return undefined;
    }
    const filePath = path.join(basePath, wordlistName);
    try {
      if (!fs.existsSync(filePath)) {
        logger.error(`词库文件不存在: ${filePath}`);
        return undefined;
      }

      const tokenizer = Tokenizer.fromFile(filePath);
      TokenUtils.tokenizerMap.set(wordlistName, tokenizer);
      logger.info(`加载词库:${filePath}成功`);
      return tokenizer;
    } catch (error) {
      logger.error(`加载词库文件失败: ${filePath}`, error instanceof Error ? error : undefined);
      return undefined;
    }
  }

  /**
   * 获取消息列表中，token数量不超过maxTokens的消息列表
   * 计算完
   * @param messages 消息列表
   * @param wordlistName 模型词库
   * @param maxTokens 最大token数量
   * @returns 消息列表
   */
  static async calculateMultimodalMessages(messages: AgentModelMessage[], wordlistName: string, imageAlgoType: string, maxTokens: number): Promise<AgentModelMessage[]> {
    let tokenCount = 0;
    const result: AgentModelMessage[] = [];
    // 先把所有system消息且不是文件资源类型需要的token计算出来，因为这些消息必须保留
    for (const message of messages) {
      if (message.message_role === 'system' && message.biz_type === '') {
        const { content } = JSON.parse(message.content);
        tokenCount += await this.token(content, wordlistName);
      }
    }
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      if (message.message_role === 'system' && message.biz_type === '') {
        // 对于syetem消息，且不是文件消息，直接添加到结果列表中
        result.unshift(message);
        continue;
      } else if (tokenCount > maxTokens) {
        // 如果token数量超过最大值，则跳过。之所以不直接break，是因为可能存在system消息，需要保留
        continue;
      }
      if (message.biz_type == "image_url") {
        const { content } = JSON.parse(message.content);
        // 获取base64编码的图片的宽高
        const base64 = content.split(',')[1];
        if (!base64) {
          logger.error('无法解析图片的base64数据');
          throw new Error('无法解析图片的base64数据');
        }
        try {
          // 将base64字符串转换为Uint8Array
          const binaryString = atob(base64);
          const bytes = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }
          const dimensions = imageSize(bytes); // 获取图片尺寸
          tokenCount += this.getImageTokens(imageAlgoType, dimensions.width, dimensions.height, 'auto');
          if (tokenCount < maxTokens && result.length > 0) {
            result.unshift(message);
          }
        } catch (error: any) {
          logger.error('无法解析图片的尺寸', error);
          throw new Error('无法解析图片的尺寸');
        }

      } else {
        const { content } = JSON.parse(message.content);
        tokenCount += await this.token(content, wordlistName);
        if (tokenCount < maxTokens) {
          result.unshift(message);
        }
      }
    }
    // 如果第一条数据类型是tool，则需要移除，确保第一条数据的role类型不是tool
    while (result.length > 0 && result[0].message_role == "tool") {
      result.shift();
    }
    return result;
  }

  /**
   * 获取消息列表中，token数量不超过maxTokens的消息列表
   * 非多模态消息，只计算文本token
   * @param messages 消息列表
   * @param wordlistName 模型词库
   * @param maxTokens 最大token数量
   * @returns 消息列表
   */
  static async calculateMessages(messages: AgentModelMessage[], wordlistName: string, maxTokens: number): Promise<AgentModelMessage[]> {
    let tokenCount = 0;
    const result: AgentModelMessage[] = [];
    // 先把所有system消息需要的token计算出来，因为system消息必须保留
    for (const message of messages) {
      if (message.message_role === 'system' && message.biz_type === '') {
        const { content } = JSON.parse(message.content);
        tokenCount += await this.token(content, wordlistName);
      }
    }
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      if (message.message_role === 'system' && message.biz_type === '') {
        // 对于syetem消息，直接添加到结果列表中
        result.unshift(message);
        continue;
      } else if (tokenCount > maxTokens) {
        // 如果token数量超过最大值，则跳过。之所以不直接break，是因为可能存在system消息，需要保留
        continue;
      }
      const { content } = JSON.parse(message.content);
      tokenCount += await this.token(content, wordlistName);
      if (tokenCount < maxTokens) {
        result.unshift(message);
      }
    }
    // 如果第一条数据类型是tool，则需要移除，确保第一条数据的role类型不是tool
    while (result.length > 0 && result[0].message_role == "tool") {
      result.shift();
    }
    return result;
  }

  /**
   * 获取图像消耗的token数量
   * @param model 模型名称
   * @param imageAlgoType 图像算法类型
   * @param width 图像宽度
   * @param height 图像高度
   * @param detail 处理模式
   * @returns 图像消耗的token数量
   */
  static getImageTokens(imageAlgoType: string, width: number, height: number, detail = 'high') {
    if (imageAlgoType === 'scale28') {
      return this.scale28ImageTokens(width, height);
    } else if (imageAlgoType === 'scale32') {
      return this.scale32ImageTokens(width, height);
    } else if (imageAlgoType === 'gpt4o') {
      return this.gpt4oImageTokens(width, height, detail);
    } else {
      throw new Error('未知的图片算法类型' + imageAlgoType);
    }
  }

  /**
* 计算图像在 OpenAI 视觉 API 中的 token 消耗（完整处理所有情况）
*
* @param {number} width - 图像宽度(像素)
* @param {number} height - 图像高度(像素)
* @param {string} [detail='high'] - 处理模式("high"或"low")
* @returns {number} 图像消耗的 token 数量
*
* 处理逻辑:
* - 低精度模式: 固定 85 tokens
* - 高精度模式:
* 1. 先缩放至长边不超过 2048 像素（保持宽高比）
* 2. 如果短边 > 768 像素，则缩放至短边 = 768 像素
* 3. 如果短边 ≤ 768 像素，保持原尺寸
* 4. 计算需要的 512x512 瓦片数量
* 5. 总 tokens = 瓦片数 × 170 + 85
*/
  static gpt4oImageTokens(width: number, height: number, detail = 'high') {
    if (detail === 'low') {
      // 85+7+300
      // 85是模型官方给的固定值，7是大模型额外占用的值，300是我们额外加的一个缓冲
      return 392;
    }

    // 第一步缩放：长边不超过 2048 像素
    const maxInitialSize = 2048;
    if (Math.max(width, height) > maxInitialSize) {
      const scale = maxInitialSize / Math.max(width, height);
      width = Math.floor(width * scale);
      height = Math.floor(height * scale);
    }

    // 第二步缩放：仅当短边 > 768 时才缩放至 768
    const minSide = Math.min(width, height);
    const targetMinSide = 768;
    if (minSide > targetMinSide) {
      const scale = targetMinSide / minSide;
      width = Math.floor(width * scale);
      height = Math.floor(height * scale);
    }

    // 计算需要的 512x512 瓦片数量（使用向上取整）
    const tileSize = 512;
    const tilesWidth = Math.ceil(width / tileSize);
    const tilesHeight = Math.ceil(height / tileSize);
    const totalTiles = tilesWidth * tilesHeight;

    return totalTiles * 170 + 85;
  }

  /**
   * Qwen2.5-VL-72B-Instruct模型，图像token计算
   * @param width 
   * @param height 
   * @returns 
   */
  static scale28ImageTokens(width: number, height: number): number {
    return Math.ceil(width * height / (28 * 28));
  }

  /**
   * 缩放32算法
   * @param width 
   * @param height 
   * @returns 
   */
  static scale32ImageTokens(width: number, height: number): number {
    return Math.ceil(width * height / (32 * 32));
  }
}
