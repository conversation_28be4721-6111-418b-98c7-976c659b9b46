import { Tool } from "../model/LLModel";
import { TokenUtils } from "./tokenUtil";

export async function tokenTools(tools: Tool[], model: string, wordlistName: string): Promise<number> {
    if (model.startsWith("gpt")) {
        const { toolStr, extraTokens } = transformToolsToGpt(tools, model);
        const token = await TokenUtils.token(toolStr, wordlistName);
        return token + extraTokens;
    } else if (model.startsWith("deepseek")) {
        return TokenUtils.token(JSON.stringify(tools), wordlistName);
    }
    return TokenUtils.token(JSON.stringify(tools), wordlistName);
}

/**
 * 将工具转换为gpt的格式
 * @param tools 工具列表
 * @param model 模型名称
 * @returns 转换后的字符串
 */
function transformToolsToGpt(tools: Tool[], model: string): { toolStr: string, extraTokens: number } {
    let extraTokens = 0;
    let func_init = 0;
    let prop_init = 0;
    let prop_key = 3;
    let enum_init = 0;
    let enum_item = 0;
    let func_end = 0;
    let str = "";
    if (model === "gpt-4o" || model === "gpt-4o-mini") {
        func_init = 7;
        prop_init = 3;
        prop_key = 3;
        enum_init = -3;
        enum_item = 3;
        func_end = 12;
    } else if (model === "gpt-3.5-turbo" || model === "gpt-4") {
        func_init = 10;
        prop_init = 3;
        prop_key = 3;
        enum_init = -3;
        enum_item = 3;
        func_end = 12;
    }
    for (const f of tools) {
        if (!f?.function) continue;
        
        extraTokens += func_init;
        const f_name = f.function.name || "";
        let f_desc = f.function.description || "";
        if (f_desc?.endsWith(".")) {
            f_desc = f_desc.slice(0, -1);
        }
        const line = `${f_name}:${f_desc}`;
        str += line;
        
        if (f.function.parameters?.properties && Object.keys(f.function.parameters.properties).length > 0) {
            extraTokens += prop_init;
        }
        
        for (const key of Object.keys(f.function.parameters?.properties || {})) {
            extraTokens += prop_key;
            const p_name = key;
            const property = f.function.parameters?.properties?.[key];
            if (!property) continue;
            
            const p_type = property.type || "";
            let p_desc = property.description || "";
            if (p_desc?.endsWith(".")) {
                p_desc = p_desc.slice(0, -1);
            }
            if (property.items) {
                extraTokens += enum_init;
                extraTokens += enum_item;
                str += property.items.type || "";
            }
            str += `${p_name}:${p_type}:${p_desc}`;
        }
        extraTokens += func_end;
    }
    return { toolStr: str, extraTokens: extraTokens };
}

