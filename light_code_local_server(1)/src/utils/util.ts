import { randomBytes } from 'crypto';
import path from 'path';

// 格式化时间
export function formatDateTime(): string {
    const date: Date = new Date();
    const year: number = date.getFullYear();
    const month: string = String(date.getMonth() + 1).padStart(2, '0');
    const day: string = String(date.getDate()).padStart(2, '0');
    const hours: string = String(date.getHours()).padStart(2, '0');
    const minutes: string = String(date.getMinutes()).padStart(2, '0');
    const seconds: string = String(date.getSeconds()).padStart(2, '0');
    const milliseconds: string = String(date.getMilliseconds()).padStart(3, '0') + '0000';  // 补充到7位

    return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}`;
}
// 添加一个生成UUID的函数
export function generateUUID(): string {
    return randomBytes(16).toString('hex');
}

export function arePathsEqual(path1?: string, path2?: string): boolean {
    if (!path1 && !path2) {
        return true;
    }
    if (!path1 || !path2) {
        return false;
    }

    // 规范化路径
    path1 = normalizePath(path1);
    path2 = normalizePath(path2);

    // 处理 UNC 路径
    if (path1.startsWith('\\\\') || path2.startsWith('\\\\')) {
        return path1.toLowerCase() === path2.toLowerCase();
    }

    // 处理网络路径
    if (path1.startsWith('//') || path2.startsWith('//')) {
        return path1.toLowerCase() === path2.toLowerCase();
    }

    // 转换为绝对路径
    try {
        path1 = path.resolve(path1);
        path2 = path.resolve(path2);
    } catch (error) {
        // 如果路径解析失败，保持原样
    }

    // Windows 环境下不区分大小写
    if (process.platform === 'win32') {
        return path1.toLowerCase() === path2.toLowerCase();
    }

    // macOS 环境下区分大小写
    return path1 === path2;
}

function normalizePath(p: string): string {
    if (!p) {
        return '';
    }

    // 替换所有路径分隔符为当前系统的分隔符
    p = p.replace(/[\\/]/g, path.sep);

    // 规范化路径
    let normalized = path.normalize(p);

    // 移除末尾的分隔符（除非是根目录）
    if (normalized.length > 1 && (normalized.endsWith(path.sep))) {
        normalized = normalized.slice(0, -1);
    }

    // 处理 Windows 的驱动器路径
    if (process.platform === 'win32') {
        // 将驱动器字母统一为大写
        if (normalized.match(/^[a-zA-Z]:/)) {
            normalized = normalized.charAt(0).toUpperCase() + normalized.slice(1);
        }
    }

    return normalized;
}