import { logger } from '../src/utils/Logger';
import { GlobalStore } from '../src/GlobalStore';
import { LOG_DIR } from '../src/GlobalStore';
import { executeMcpToolWithCallback, loadMcp } from '../src/mcp/mcp';
import { IdeClient } from '../src/model/IdeClient';
import { ToolCallResult } from '../src/agent/tool/ToolCallResult';

console.log("开始测试MCP");
logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));
const client = new IdeClient("测试客户端");
client.mcpConfigs = new Map([
    ["mcp_gaode", {
        id: "mcp_gaode",
        name: "mcp_gaode",
        url: "https://mcp.amap.com/sse?key=4c14c7c745851bc017c05c3bd9addb0c",
        transportType: "sse",
        timeout: 5000,
    }]
]);
loadMcp(client).then(() => {
    console.log("MCP加载完成");
    const mcpConnection = client.mcpHub.connections.get("mcp_gaode");
    if (mcpConnection) {
        mcpConnection.server.toolDefines?.forEach(tool => {
            if (tool.name == "maps_distance") {
                executeMcpToolWithCallback(tool.name, "abc", {
                    origins: "116.407526,39.90872",
                    destination: "116.407526,39.90872",
                    type: "base",
                    output: "json"
                }, mcpConnection).then(result => {
                    const tcr = result as ToolCallResult;
                    console.log(tcr.result);
                });
            }
        });
    }
});

