import { TokenUtils } from '../src/utils/tokenUtil';
import { logger } from '../src/utils/Logger';
import { GlobalStore } from '../src/GlobalStore';
import { LOG_DIR } from '../src/GlobalStore';

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));
TokenUtils.loadWordList('Qwen2-72B-Instruct.json');
TokenUtils.loadWordList('o200k_base');
TokenUtils.loadWordList('deepseek-v3r1.json');
TokenUtils.loadWordList('codellama-13b-hf.json');
const content = '你好，世界！';
TokenUtils.token(content, 'Qwen2-72B-Instruct.json').then(tokens => {
    console.log(`qwen2-72b-instruct: ${tokens}`);
});
TokenUtils.token(content, 'codellama-13b-hf.json').then(tokens => {
    console.log(`codellama-13b-hf: ${tokens}`);
});
TokenUtils.token(content, 'o200k_base').then(tokens => {
    console.log(`gpt-4o: ${tokens}`);
});
TokenUtils.token(content, 'deepseek-v3r1.json').then(tokens => {
    console.log(`deepseek-v3r1: ${tokens}`);
});
TokenUtils.token(content, '123').then(tokens => {
    console.log(`123: ${tokens}`);
});