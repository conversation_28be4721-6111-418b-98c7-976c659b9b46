import { logger } from '../src/utils/Logger';
import { GlobalStore } from '../src/GlobalStore';
import { LOG_DIR } from '../src/GlobalStore';

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));

function executeTest() {
    require('./list_dir');
    require('./view_file');
    require('./grep_search');
    require('./related_files');
    require('./codebase_search');
    require('./write_to_file');
    require('./edit_file');
}
executeTest();