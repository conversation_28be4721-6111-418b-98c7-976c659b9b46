import { GlobalStore, LOG_DIR } from '../src/GlobalStore';
import { Tool } from '../src/model/LLModel';
import { logger } from '../src/utils/Logger';
import { tokenTools } from '../src/utils/toolTransUtil';

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));

const grepSearch: Tool = {
    "type": "function",
    "function": {
        "description": "Fast text-based search that finds exact pattern matches within files or directories, utilizing the ripgrep command for efficient searching. Results will be formatted in the style of ripgrep and can be configured to include line numbers and content. To avoid overwhelming output, the results are capped at 50 matches. Use the Includes option to filter the search scope by file types or specific paths to narrow down the results.",
        "name": "grep_search",
        "parameters": {
            "additionalProperties": false,
            "properties": {
                "caseInsensitive": {
                    "description": "If true, performs a case-insensitive search.",
                    "type": "boolean"
                },
                "includes": {
                    "description": "The files or directories to search within. Supports file patterns (e.g., \u0027*.txt\u0027 for all .txt files) or specific paths (e.g., \u0027path/to/file.txt\u0027 or \u0027path/to/dir\u0027).",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "matchPerLine": {
                    "description": "If true, returns each line that matches the query, including line numbers and snippets of matching lines (equivalent to \u0027git grep -nI\u0027). If false, only returns the names of files containing the query (equivalent to \u0027git grep -l\u0027).",
                    "type": "boolean"
                },
                "query": {
                    "description": "The search term or pattern to look for within files.",
                    "type": "string"
                },
                "searchDirectory": {
                    "description": "The directory from which to run the ripgrep command. This path must be a directory not a file.",
                    "type": "string"
                }
            },
            "required": [
                "caseInsensitive",
                "includes",
                "matchPerLine",
                "query",
                "searchDirectory"
            ],
            "type": "object"
        }
    }
};
const viewFile: Tool = {
    "type": "function",
    "function": {
        "description": "View the contents of a file. The lines of the file are 0-indexed, and the output of this tool call will be the file contents from StartLine to EndLine, together with a summary of the lines outside of StartLine and EndLine. Note that this call can view at most 200 lines at a time.\n\nWhen using this tool to gather information, it\u0027s your responsibility to ensure you have the COMPLETE context. Specifically, each time you call this command you should:\n1) Assess if the file contents you viewed are sufficient to proceed with your task.\n2) Take note of where there are lines not shown. These are represented by \u003c... XX more lines from [code item] not shown ...\u003e in the tool response.\n3) If the file contents you have viewed are insufficient, and you suspect they may be in lines not shown, proactively call the tool again to view those lines.\n4) When in doubt, call this tool again to gather more information. Remember that partial file views may miss critical dependencies, imports, or functionality.",
        "name": "view_file",
        "parameters": {
            "additionalProperties": false,
            "properties": {
                "absolutePath": {
                    "description": "Path to file to view. Must be an absolute path",
                    "type": "string"
                },
                "startLine": {
                    "description": "Startline to view",
                    "type": "integer"
                },
                "endLine": {
                    "description": "Endline to view. This cannot be more than 200 lines away from StartLine",
                    "type": "integer"
                }
            },
            "required": [
                "absolutePath",
                "startLine",
                "endLine"
            ],
            "type": "object"
        }
    }
};
const writeToFile: Tool = {
    "type": "function",
    "function": {
        "description": "Use this tool to create new files. The file and any parent directories will be created for you if they do not already exist.\n\t\tFollow these instructions:\n\t\t1. NEVER use this tool to modify or overwrite existing files. Always first confirm that TargetFile does not exist before calling this tool.\n\t\t2. You MUST specify TargetFile as the FIRST argument. Please specify the full TargetFile before any of the code contents.\nYou should specify the following arguments before the others: [TargetFile]",
        "name": "write_to_file",
        "parameters": {
            "additionalProperties": false,
            "properties": {
                "targetFile": {
                    "description": "The target file to create and write code to.",
                    "type": "string"
                },
                "codeContent": {
                    "description": "The code contents to write to the file.",
                    "type": "string"
                },
                "emptyFile": {
                    "description": "Set this to true to create an empty file.",
                    "type": "boolean"
                }
            },
            "required": [
                "targetFile",
                "codeContent",
                "emptyFile"
            ],
            "type": "object"
        }
    }
};

function executeTest() {
    const tools = [grepSearch, viewFile, writeToFile];
    tokenTools(tools, 'gpt-4o', 'o200k_base').then((token) => {
        console.log(token);
    });

    tokenTools(tools, 'deepseek-v3', 'deepseek-v3r1.json').then((token) => {
        console.log(token);
    });
}

executeTest();