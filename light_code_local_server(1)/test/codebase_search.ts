import { codebase_search } from '../src/agent/tool/tools';
import { logger } from '../src/utils/Logger';
import { GlobalStore } from '../src/GlobalStore';
import { LOG_DIR } from '../src/GlobalStore';

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));
export function normalTest() {
    const currentDir = __dirname;
    console.log("================================================");
    console.log('codebase_search正常执行测试');
    // 正常执行
    const result = codebase_search({
        query: 'c',
        targetDirectories: [currentDir],
    });
    // console.log(result);
    console.log("查询结果数量：" + result.length);
    console.log('codebase_search正常执行测试完成');
}

export function errorTest() {
    console.log("================================================");
    // 错误目录
    console.log('codebase_search错误目录测试');
    try {
        codebase_search({
            query: 'codebase_search',
            targetDirectories: ['mistake'],
        });
    } catch (error: any) {
        console.log(error.message);
    }
    console.log('codebase_search错误目录测试完成');
}

export function missingParamTest() {
    console.log("================================================");
    // 缺少参数query
    console.log('codebase_search缺少参数query测试');
    try {
        codebase_search({
            query: '',
            targetDirectories: ['mistake'],
        });
    } catch (error: any) {
        console.log(error.message);
    }

    try {
        codebase_search({
            query: 'aaa',
            targetDirectories: [],
        });
    } catch (error: any) {
        console.log(error.message);
    }
    console.log('缺少参数query测试完成');
}

normalTest();
// errorTest();
// missingParamTest();

