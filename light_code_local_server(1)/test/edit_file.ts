import path from 'path';
import { edit_file } from '../src/agent/tool/tools';
import { GlobalStore, LOG_DIR } from '../src/GlobalStore';
import { logger } from '../src/utils/Logger';
import { IdeClient } from '../src/model/IdeClient';
import { AgentStorage } from '../src/agent/AgentStorage';

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));

export function normalTest() {
    const currentDir = __dirname;
    console.log("================================================");
    const client = new IdeClient("test");
    client.baseUrl = "http://10.20.42.108:8053/";
    client.projectDir = currentDir;
    client.agentStorge = new AgentStorage("1", "1", 1, [], "1", "1", "1", [], "1", false);
    client.agentStorge.getOrigiFileContentMap().set(path.resolve(currentDir, 'test_for_edit.txt'), {
        filePath: path.resolve(currentDir, 'test_for_edit.txt'),
        originFileCachePath: path.resolve(currentDir, 'test_for_edit.txt'),
        diffCount: 10000
    });
    const result = edit_file({
        targetFile: path.resolve(currentDir, 'test_for_edit.txt'),
        codeEdit: 'test',
    }, '1', '1', '1', client);

    console.log('edit_file正常执行测试完成');
}
export function errorTest() {
    console.log("================================================");
    // 错误目录
    const client = new IdeClient("test");
    console.log('edit_file错误目录测试');
    try {
        const result = edit_file({
        targetFile: '',
        codeEdit: 'test',
    }, '1', '1', '1', client);
    } catch (error: any) {
        console.log(error.message);
    }

    try {
        const result = edit_file({
            targetFile: 'aaa',
            codeEdit: '',
        }, '1', '1', '1', client);
    } catch (error: any) {
        console.log(error.message);
    }
    console.log('edit_file错误目录测试完成');
}
// errorTest();
normalTest();
