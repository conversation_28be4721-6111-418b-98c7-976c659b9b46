import { grep_search } from '../src/agent/tool/tools';
import { logger } from '../src/utils/Logger';
import { GlobalStore } from '../src/GlobalStore';
import { LOG_DIR } from '../src/GlobalStore';

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));
export function normalTest() {
    const currentDir = __dirname;
    console.log("================================================");
    console.log('grep_search正常执行测试');
    const result = grep_search({
        searchDirectory: currentDir,
        query: 'a',
        caseInsensitive: false,
        includes: ['grep_search.ts'],
        matchPerLine: true,
    });
    console.log("查询结果数量：" + result.length + "所在行：" + result[0].line);

    const result1 = grep_search({
        searchDirectory: currentDir,
        query: '*',
        caseInsensitive: false,
        includes: ['grep_search.ts'],
        matchPerLine: false,
    });
    console.log("查询结果数量：" + result1.length + "所在行：" + result1[0].line);

    console.log('grep_search正常执行测试完成');
}

export function paramCheckTest() {
    console.log("================================================");
    console.log('grep_search参数检查执行测试');
    try {
        grep_search({
            searchDirectory: '',
            query: '',
            caseInsensitive: false,
            includes: ['grep_search.ts'],
            matchPerLine: true,
        });
    } catch (error: any) {
        console.log(error.message);
    }
    try {
        grep_search({
            searchDirectory: 'aaa',
            query: '*',
            caseInsensitive: false,
            includes: ['grep_search.ts'],
            matchPerLine: true,
        });
    } catch (error: any) {
        console.log(error.message);
    }
    console.log('grep_search参数检查执行测试完成');
}

normalTest();
paramCheckTest();

