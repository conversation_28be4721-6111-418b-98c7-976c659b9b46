import { list_dir } from '../src/agent/tool/tools';
import { logger } from '../src/utils/Logger';
import { GlobalStore } from '../src/GlobalStore';
import { LOG_DIR } from '../src/GlobalStore';

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));
export function normalTest() {
    //获取当前文件目录的父目录的路径
    const currentDir = __dirname;
    console.log("================================================");
    // 正常执行
    console.log('list_dir正常执行测试');
    const result = list_dir({
        dirPath: currentDir,
    });
    console.log("查询结果数量：" + result.length);
    console.log('list_dir正常执行测试完成');
}
export function errorTest() {
    console.log("================================================");
    // 错误目录
    console.log('list_dir错误目录测试');
    try {
        list_dir({
            dirPath: 'mistake',
        });
    } catch (error: any) {
        console.log(error.message);
    }

    try {
        list_dir({
            dirPath: '',
        });
    } catch (error: any) {
        console.log(error.message);
    }
    console.log('list_dir错误目录测试完成');
}

normalTest();
errorTest();


