import { related_files } from '../src/agent/tool/tools';
import { logger } from '../src/utils/Logger';
import { GlobalStore } from '../src/GlobalStore';
import { LOG_DIR } from '../src/GlobalStore';

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));
export function normalTest() {
    const currentDir = __dirname;
    // 正常执行
    console.log("================================================");
    console.log('related_files正常执行测试');
    console.log(currentDir );
    const result = related_files({
        absolutePath: currentDir + '/related_files.ts',
    });
    console.log(result);
    console.log("查询结果数量：" + result.length);

    console.log('related_files正常执行测试完成');
}
export function errorTest() {
    console.log("================================================");
    // 错误目录
    console.log('related_files错误目录测试');
    try {
        related_files({
            absolutePath: '',
        });
    } catch (error: any) {
        console.log(error.message);
    }

    try {
        related_files({
            absolutePath: 'aaa',
        });
    } catch (error: any) {
        console.log(error.message);
    }
    console.log('related_files错误目录测试完成');
}

normalTest();
// errorTest();

