import { view_file } from '../src/agent/tool/tools';
import { logger } from '../src/utils/Logger';
import { GlobalStore } from '../src/GlobalStore';
import { LOG_DIR } from '../src/GlobalStore';

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));
export function normalTest() {
    const currentDir = __dirname;
    console.log("================================================");
    console.log('view_file正常执行测试');
    const result = view_file({
        absolutePath: currentDir + '/grep_search.ts',
        startLine: 0,
        endLine: 200,
    });
    if (result.includes('all lines from file')) {
        console.log("内容已经全部读取");
    }

    const result1 = view_file({
        absolutePath: currentDir + '/grep_search.ts',
        startLine: 0,
        endLine: 10,
    });
    if (result1.includes('more lines from')) {
        console.log("内容已经部分读取");
    }

    const result2 = view_file({
        absolutePath: currentDir + '/grep_search.ts',
        startLine: 0,
        endLine: 0,
    });
    console.log(result2);
    console.log('view_file正常执行测试完成');
}

export function overStartTest() {
    const currentDir = __dirname;
    console.log("================================================");
    console.log('view_file起始行超出范围执行测试');
    try {
        view_file({
            absolutePath: currentDir + '/grep_search.ts',
            startLine: 10000,
            endLine: 10000,
        });
    } catch (error: any) {
        console.log(error.message);
    }
    console.log('view_file起始行超出范围执行测试完成');
}

export function paramCheckTest() {
    const currentDir = __dirname;
    console.log("================================================");
    console.log('view_file参数检查执行测试');
    try {
        view_file({
            absolutePath: 'aaaa',
            startLine: 1,
            endLine: 10,
        });
    } catch (error: any) {
        console.log(error.message);
    }
    try {
        view_file({
            absolutePath: '',
            startLine: 1,
            endLine: 10,
        });
    } catch (error: any) {
        console.log(error.message);
    }
    try {
        view_file({
            absolutePath: currentDir + '/grep_search.ts',
            startLine: -1,
            endLine: 10,
        });
    } catch (error: any) {
        console.log(error.message);
    }
    try {
        view_file({
            absolutePath: currentDir + '/grep_search.ts',
            startLine: 1,
            endLine: -10,
        });
    } catch (error: any) {
        console.log(error.message);
    }
    try {
        view_file({
            absolutePath: currentDir + '/grep_search.ts',
            startLine: 1,
            endLine: 1000,
        });
    } catch (error: any) {
        console.log(error.message);
    }
    console.log('view_file参数检查执行测试完成');
}

normalTest();
overStartTest();
paramCheckTest();
