import path from 'path';
import fs from 'fs';
import { write_to_file } from '../src/agent/tool/tools';
import { logger } from '../src/utils/Logger';
import { GlobalStore } from '../src/GlobalStore';
import { LOG_DIR } from '../src/GlobalStore';
import { AgentStorage } from '../src/agent/AgentStorage';
import { IdeClient } from '../src/model/IdeClient';

logger.init(GlobalStore.getStore().getStaticValue(LOG_DIR));

export function normalTest() {
    const currentDir = __dirname;
    const client = new IdeClient("test");
    client.baseUrl = "http://10.20.42.108:8053/";
    client.projectDir = currentDir;
    client.agentStorge = new AgentStorage("1", "1", 1, [], "1", "1", "1", [], "1", false);
    client.agentStorge.getOrigiFileContentMap().set(path.resolve(currentDir, 'test_for_edit.txt'), {
        filePath: path.resolve(currentDir, 'test_for_edit.txt'),
        originFileCachePath: path.resolve(currentDir, 'test_for_edit.txt'),
        diffCount: 10000
    });
    console.log("================================================");
    console.log('write_to_file正常执行测试');
    if (fs.existsSync(path.resolve(currentDir, 'test.txt'))) {
        // 如果文件存在，则删除文件
        fs.unlinkSync(path.resolve(currentDir, 'test.txt'));
    }
    const result = write_to_file({
        targetFile: path.resolve(currentDir, 'test.txt'),
        codeContent: 'test',
        emptyFile: false,
    }, '1', client);
    if (fs.existsSync(path.resolve(currentDir, 'test.txt'))) {
        console.log("文件写入成功，在当前目录下发现该文件");
        // 如果文件存在，则删除文件
        fs.unlinkSync(path.resolve(currentDir, 'test.txt'));
    }
    console.log('write_to_file正常执行测试完成');
}
export function errorTest() {
    const currentDir = __dirname;
    const client = new IdeClient("test");
    client.baseUrl = "http://10.20.42.108:8053/";
    client.projectDir = currentDir;
    client.agentStorge = new AgentStorage("1", "1", 1, [], "1", "1", "1", [], "1", false);
    client.agentStorge.getOrigiFileContentMap().set(path.resolve(currentDir, 'test_for_edit.txt'), {
        filePath: path.resolve(currentDir, 'test_for_edit.txt'),
        originFileCachePath: path.resolve(currentDir, 'test_for_edit.txt'),
        diffCount: 10000
    });
    console.log("================================================");
    // 错误目录
    console.log('write_to_file错误目录测试');
    try {
        write_to_file({
            targetFile: '',
            codeContent: 'test',
            emptyFile: false,
        }, '1', client);
    } catch (error: any) {
        console.log(error.message);
    }

    try {
        write_to_file({
            targetFile: 'aaa',
            codeContent: '',
            emptyFile: false,
        }, '1', client);
    } catch (error: any) {
        console.log(error.message);
    }

    try {
        write_to_file({
            targetFile: path.resolve(currentDir, 'write_to_file.ts'),
            codeContent: 'aaa',
            emptyFile: false,
        }, '1', client);
    } catch (error: any) {
        console.log(error.message);
    }
    console.log('write_to_file错误目录测试完成');
}
normalTest();
errorTest();
