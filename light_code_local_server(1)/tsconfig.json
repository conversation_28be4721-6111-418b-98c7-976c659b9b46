{
  // TypeScript 编译器的配置选项
  "compilerOptions": {
    // 编译后的 JavaScript 目标版本
    "target": "ES2020",
    // 指定生成代码的模块系统
    "module": "commonjs",
    // 编译后的输出目录
    "outDir": "./dist",
    // TypeScript 源文件所在的根目录
    "rootDir": "./src",
    // 编译过程中需要引入的库文件
    "lib": ["ES2020", "dom"],
    // 启用所有严格的类型检查选项
    "strict": true,
    // 允许使用 import 导入 CommonJS 模块
    "esModuleInterop": true,
    // 跳过声明文件的类型检查
    "skipLibCheck": true,
    // 强制文件名大小写一致
    "forceConsistentCasingInFileNames": true
  },
  // 需要编译的文件路径
  "include": ["src/**/*"],
  // 不需要编译的文件路径
  "exclude": ["node_modules"]
} 