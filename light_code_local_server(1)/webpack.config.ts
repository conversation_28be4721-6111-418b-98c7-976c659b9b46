import * as path from 'path';
import * as webpack from 'webpack';
import CopyWebpackPlugin from 'copy-webpack-plugin';

// 使用 require 语法导入 nodeExternals
const nodeExternals = require('webpack-node-externals');

const config: webpack.Configuration = {
  // 构建模式：production 表示生产环境构建，会进行代码优化
  mode: 'production',
  // 目标环境：指定为 Node.js 18 版本
  target: 'node18',
  // 入口文件：指定项目的入口文件路径
  entry: './src/index.ts',
  // 优化配置
  optimization: {
    // 不压缩代码，保持可读性
    minimize: false,
    // 不进行代码分割
    splitChunks: false,
    // 不生成运行时文件
    runtimeChunk: false
  },
  // 模块配置
  module: {
    rules: [
      {
        // 匹配 TypeScript 和 TSX 文件
        test: /\.tsx?$/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: [
              // 配置 Babel 预设
              ['@babel/preset-env', {
                targets: {
                  node: '18.0.0'
                }
              }],
              '@babel/preset-typescript'
            ],
            // 使用运行时转换插件
            plugins: ['@babel/plugin-transform-runtime']
          }
        },
        // 排除 node_modules 目录
        exclude: [/node_modules/, /test/],
      },
    ],
  },
  // 解析配置
  resolve: {
    // 自动解析的文件扩展名
    extensions: ['.tsx', '.ts', '.js'],
    // 指定模块的主字段
    mainFields: ['main', 'module'],
    // 模块解析的目录
    modules: ['node_modules'],
    // 路径别名配置
    alias: {
      'axios': path.resolve(__dirname, 'node_modules/axios/dist/node/axios.cjs'),
      'eventsource': path.resolve(__dirname, 'node_modules/eventsource/lib/eventsource.js'),
      'abort-controller': path.resolve(__dirname, 'node_modules/abort-controller/dist/abort-controller.js'),
      'encoding': path.resolve(__dirname, 'node_modules/iconv-lite')
    }
  },
  // 输出配置
  output: {
    // 输出文件名
    filename: 'lightcode.js',
    // 输出目录
    path: path.resolve(__dirname, 'dist'),
    // 每次构建前清理输出目录
    clean: true
  },
  // 插件配置
  plugins: [
    // 定义全局常量
    new webpack.DefinePlugin({
      'process.env.PACKAGE_CONFIG': JSON.stringify({
        name: 'lightcode',
        version: '1.0.0',
        type: 'commonjs'
      })
    }),
    // 自动加载模块
    new webpack.ProvidePlugin({
      Event: ['events', 'Event'],
      CustomEvent: ['events', 'CustomEvent'],
      AbortController: ['abort-controller', 'AbortController'],
      AbortSignal: ['abort-controller', 'AbortSignal'],
      'global.AbortController': ['abort-controller', 'AbortController'],
      'global.AbortSignal': ['abort-controller', 'AbortSignal']
    }),
    // 复制静态资源
    new CopyWebpackPlugin({
      patterns: [
        { 
          from: 'resources', 
          to: 'resources',
          globOptions: {
            ignore: ['**/.gitkeep']
          }
        }
      ]
    })
  ],
  // 外部依赖配置
  externals: [nodeExternals({
    // 允许打包的依赖列表
    allowlist: [
      'axios',
      '@modelcontextprotocol/sdk',
      'eventsource',
      '@grpc/grpc-js',
      '@grpc/proto-loader',
      'tiktoken',
      'zod',
      'fast-deep-equal',
      'glob',
      'chokidar',
      'node-fetch',
      'abort-controller',
      'encoding'
    ]
  })],
  // Node.js 全局变量配置
  node: {
    __dirname: false,
    __filename: false,
    global: true
  }
};

export default config; 