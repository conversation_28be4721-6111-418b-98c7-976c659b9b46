# TodoList 待办事项管理应用

一个功能完整、界面现代化的待办事项管理应用，支持任务的增删改查、优先级设置、批量操作等功能。

## 🚀 功能特性

### 📝 任务管理
- **添加任务**：快速添加新的待办事项
- **编辑任务**：双击或点击编辑按钮修改任务内容
- **删除任务**：单个删除或批量删除
- **完成标记**：勾选复选框标记任务完成状态

### 🎯 优先级管理
- **三级优先级**：高、中、低优先级设置
- **优先级排序**：按优先级对任务进行排序
- **颜色标识**：不同优先级用不同颜色标识

### 🔍 搜索与过滤
- **实时搜索**：输入关键词实时搜索任务
- **状态过滤**：查看全部、待完成、已完成任务
- **多种排序**：按创建时间、优先级、字母顺序排序

### 📊 统计信息
- **任务统计**：显示总任务数、已完成数、待完成数
- **实时更新**：统计信息随任务变化实时更新

### 🔧 批量操作
- **批量选择**：支持多选任务
- **批量完成**：一键标记多个任务为完成
- **批量删除**：一键删除多个任务

### 💾 数据持久化
- **本地存储**：使用localStorage保存数据
- **自动保存**：任务变更自动保存到本地

### ⌨️ 快捷键支持
- **Ctrl/Cmd + Enter**：快速添加任务
- **Ctrl/Cmd + A**：全选可见任务
- **Escape**：关闭模态框

## 📁 文件结构

```
todolist-app/
├── index.html          # 主页面文件
├── styles.css          # 样式文件
├── script.js           # JavaScript功能
└── README.md          # 说明文档
```

## 🎨 界面设计

### 现代化UI
- **渐变背景**：美观的渐变色背景
- **卡片式设计**：清晰的卡片布局
- **响应式设计**：适配桌面端和移动端

### 交互体验
- **流畅动画**：平滑的过渡动画效果
- **悬停效果**：鼠标悬停时的视觉反馈
- **加载状态**：操作过程中的状态提示

## 🛠️ 使用方法

### 1. 直接打开
直接在浏览器中打开 `index.html` 文件即可使用。

### 2. 本地服务器
为了获得最佳体验，建议使用本地服务器：

```bash
# 使用Python
python -m http.server 8000

# 使用Node.js
npx http-server

# 使用Live Server (VS Code扩展)
右键点击index.html -> Open with Live Server
```

### 3. 基本操作

#### 添加任务
1. 在输入框中输入任务内容
2. 选择优先级（低、中、高）
3. 点击"添加"按钮或按Ctrl+Enter

#### 管理任务
- **完成任务**：点击任务前的复选框
- **编辑任务**：点击编辑按钮（铅笔图标）
- **删除任务**：点击删除按钮（垃圾桶图标）

#### 搜索和过滤
- **搜索**：在搜索框中输入关键词
- **过滤**：点击"全部"、"待完成"、"已完成"按钮
- **排序**：使用排序下拉菜单选择排序方式

#### 批量操作
1. 勾选任务前的选择框
2. 在底部批量操作栏中选择操作
3. 确认执行批量操作

## 🔧 自定义配置

### 修改主题颜色
在 `styles.css` 中修改CSS变量：

```css
/* 主色调 */
background: linear-gradient(135deg, #您的颜色1 0%, #您的颜色2 100%);

/* 按钮颜色 */
.add-btn {
    background: linear-gradient(135deg, #您的颜色1 0%, #您的颜色2 100%);
}
```

### 修改优先级设置
在 `script.js` 中修改优先级配置：

```javascript
// 优先级文本映射
getPriorityText(priority) {
    const priorityMap = {
        high: '紧急',
        medium: '普通',
        low: '不急'
    };
    return priorityMap[priority] || '普通';
}
```

### 添加新功能
应用采用模块化设计，可以轻松扩展新功能：

```javascript
// 在TodoApp类中添加新方法
newFeature() {
    // 新功能实现
}
```

## 📱 响应式支持

- **桌面端**：完整功能，最佳体验
- **平板端**：自适应布局调整
- **手机端**：优化触摸操作，简化界面

## 🌐 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 📦 依赖项

- Font Awesome 6.0.0 (图标库)
- 现代浏览器的ES6+支持
- localStorage API支持

## 🔒 数据安全

- 所有数据存储在本地浏览器中
- 不会上传到任何服务器
- 清除浏览器数据会删除所有任务

## 🚀 性能优化

- 虚拟滚动（适用于大量任务）
- 防抖搜索（避免频繁搜索）
- 懒加载动画效果
- 内存优化的事件绑定

## 📝 更新日志

### v1.0.0
- 基础任务管理功能
- 优先级设置
- 搜索和过滤
- 批量操作
- 响应式设计
- 本地数据存储

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License

## 💡 使用技巧

1. **快速添加**：使用Ctrl+Enter快捷键快速添加任务
2. **批量管理**：选中多个任务后使用批量操作提高效率
3. **优先级管理**：合理设置优先级，使用优先级排序功能
4. **搜索技巧**：使用关键词搜索快速找到特定任务
5. **定期清理**：定期删除已完成的任务保持列表整洁
