<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TodoList - 待办事项管理</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-tasks"></i>
                    <h1>TodoList</h1>
                </div>
                <div class="header-stats">
                    <div class="stat-item">
                        <span class="stat-number" id="totalTasks">0</span>
                        <span class="stat-label">总任务</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="completedTasks">0</span>
                        <span class="stat-label">已完成</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number" id="pendingTasks">0</span>
                        <span class="stat-label">待完成</span>
                    </div>
                </div>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 添加任务区域 -->
            <section class="add-task-section">
                <div class="add-task-container">
                    <form id="addTaskForm" class="add-task-form">
                        <div class="input-group">
                            <input 
                                type="text" 
                                id="taskInput" 
                                placeholder="添加新的待办事项..." 
                                maxlength="200"
                                required
                            >
                            <select id="prioritySelect" class="priority-select">
                                <option value="low">低优先级</option>
                                <option value="medium" selected>中优先级</option>
                                <option value="high">高优先级</option>
                            </select>
                            <button type="submit" class="add-btn">
                                <i class="fas fa-plus"></i>
                                添加
                            </button>
                        </div>
                    </form>
                </div>
            </section>

            <!-- 过滤和搜索区域 -->
            <section class="filter-section">
                <div class="filter-container">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="搜索待办事项...">
                    </div>
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">
                            <i class="fas fa-list"></i>
                            全部
                        </button>
                        <button class="filter-btn" data-filter="pending">
                            <i class="fas fa-clock"></i>
                            待完成
                        </button>
                        <button class="filter-btn" data-filter="completed">
                            <i class="fas fa-check"></i>
                            已完成
                        </button>
                    </div>
                    <div class="sort-options">
                        <select id="sortSelect">
                            <option value="newest">最新创建</option>
                            <option value="oldest">最早创建</option>
                            <option value="priority">优先级</option>
                            <option value="alphabetical">字母顺序</option>
                        </select>
                    </div>
                </div>
            </section>

            <!-- 任务列表区域 -->
            <section class="tasks-section">
                <div class="tasks-container">
                    <div id="tasksList" class="tasks-list">
                        <!-- 任务项将通过JavaScript动态添加 -->
                    </div>
                    
                    <!-- 空状态提示 -->
                    <div id="emptyState" class="empty-state">
                        <div class="empty-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h3>暂无待办事项</h3>
                        <p>添加您的第一个待办事项开始管理任务吧！</p>
                    </div>
                </div>
            </section>
        </main>

        <!-- 批量操作区域 -->
        <div class="bulk-actions" id="bulkActions" style="display: none;">
            <div class="bulk-actions-content">
                <span class="selected-count">已选择 <span id="selectedCount">0</span> 项</span>
                <div class="bulk-buttons">
                    <button class="bulk-btn complete-selected">
                        <i class="fas fa-check"></i>
                        标记完成
                    </button>
                    <button class="bulk-btn delete-selected">
                        <i class="fas fa-trash"></i>
                        删除选中
                    </button>
                    <button class="bulk-btn cancel-selection">
                        <i class="fas fa-times"></i>
                        取消选择
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑任务模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>编辑待办事项</h3>
                <button class="close-btn" id="closeModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="editTaskForm">
                    <div class="form-group">
                        <label for="editTaskText">任务内容</label>
                        <textarea id="editTaskText" rows="3" maxlength="200" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editPriority">优先级</label>
                        <select id="editPriority">
                            <option value="low">低优先级</option>
                            <option value="medium">中优先级</option>
                            <option value="high">高优先级</option>
                        </select>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="btn-secondary" id="cancelEdit">取消</button>
                        <button type="submit" class="btn-primary">保存更改</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 确认删除模态框 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>确认删除</h3>
                <button class="close-btn" id="closeConfirmModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p>确定要删除这个待办事项吗？此操作无法撤销。</p>
                <div class="modal-actions">
                    <button type="button" class="btn-secondary" id="cancelDelete">取消</button>
                    <button type="button" class="btn-danger" id="confirmDelete">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 消息提示 -->
    <div id="toast" class="toast">
        <div class="toast-content">
            <i class="toast-icon"></i>
            <span class="toast-message"></span>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
