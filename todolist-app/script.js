// TodoList 应用主要功能
class TodoApp {
    constructor() {
        this.tasks = JSON.parse(localStorage.getItem('todoTasks')) || [];
        this.currentFilter = 'all';
        this.currentSort = 'newest';
        this.selectedTasks = new Set();
        this.editingTaskId = null;
        this.deletingTaskId = null;
        
        this.initializeElements();
        this.bindEvents();
        this.render();
        this.updateStats();
    }

    // 初始化DOM元素
    initializeElements() {
        this.elements = {
            // 表单和输入
            addTaskForm: document.getElementById('addTaskForm'),
            taskInput: document.getElementById('taskInput'),
            prioritySelect: document.getElementById('prioritySelect'),
            searchInput: document.getElementById('searchInput'),
            sortSelect: document.getElementById('sortSelect'),
            
            // 任务列表
            tasksList: document.getElementById('tasksList'),
            emptyState: document.getElementById('emptyState'),
            
            // 过滤按钮
            filterButtons: document.querySelectorAll('.filter-btn'),
            
            // 统计
            totalTasks: document.getElementById('totalTasks'),
            completedTasks: document.getElementById('completedTasks'),
            pendingTasks: document.getElementById('pendingTasks'),
            
            // 批量操作
            bulkActions: document.getElementById('bulkActions'),
            selectedCount: document.getElementById('selectedCount'),
            completeSelectedBtn: document.querySelector('.complete-selected'),
            deleteSelectedBtn: document.querySelector('.delete-selected'),
            cancelSelectionBtn: document.querySelector('.cancel-selection'),
            
            // 模态框
            editModal: document.getElementById('editModal'),
            editTaskForm: document.getElementById('editTaskForm'),
            editTaskText: document.getElementById('editTaskText'),
            editPriority: document.getElementById('editPriority'),
            closeModal: document.getElementById('closeModal'),
            cancelEdit: document.getElementById('cancelEdit'),
            
            confirmModal: document.getElementById('confirmModal'),
            closeConfirmModal: document.getElementById('closeConfirmModal'),
            cancelDelete: document.getElementById('cancelDelete'),
            confirmDelete: document.getElementById('confirmDelete'),
            
            // 消息提示
            toast: document.getElementById('toast')
        };
    }

    // 绑定事件监听器
    bindEvents() {
        // 添加任务
        this.elements.addTaskForm.addEventListener('submit', (e) => this.handleAddTask(e));
        
        // 搜索和过滤
        this.elements.searchInput.addEventListener('input', () => this.handleSearch());
        this.elements.sortSelect.addEventListener('change', () => this.handleSort());
        
        // 过滤按钮
        this.elements.filterButtons.forEach(btn => {
            btn.addEventListener('click', () => this.handleFilter(btn.dataset.filter));
        });
        
        // 批量操作
        this.elements.completeSelectedBtn.addEventListener('click', () => this.completeSelectedTasks());
        this.elements.deleteSelectedBtn.addEventListener('click', () => this.deleteSelectedTasks());
        this.elements.cancelSelectionBtn.addEventListener('click', () => this.cancelSelection());
        
        // 编辑模态框
        this.elements.editTaskForm.addEventListener('submit', (e) => this.handleEditTask(e));
        this.elements.closeModal.addEventListener('click', () => this.closeEditModal());
        this.elements.cancelEdit.addEventListener('click', () => this.closeEditModal());
        
        // 确认删除模态框
        this.elements.closeConfirmModal.addEventListener('click', () => this.closeConfirmModal());
        this.elements.cancelDelete.addEventListener('click', () => this.closeConfirmModal());
        this.elements.confirmDelete.addEventListener('click', () => this.confirmDeleteTask());
        
        // 点击模态框外部关闭
        this.elements.editModal.addEventListener('click', (e) => {
            if (e.target === this.elements.editModal) this.closeEditModal();
        });
        this.elements.confirmModal.addEventListener('click', (e) => {
            if (e.target === this.elements.confirmModal) this.closeConfirmModal();
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));
    }

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 添加任务
    handleAddTask(e) {
        e.preventDefault();
        
        const text = this.elements.taskInput.value.trim();
        const priority = this.elements.prioritySelect.value;
        
        if (!text) {
            this.showToast('请输入待办事项内容', 'error');
            return;
        }
        
        const task = {
            id: this.generateId(),
            text: text,
            priority: priority,
            completed: false,
            createdAt: new Date().toISOString(),
            completedAt: null
        };
        
        this.tasks.unshift(task);
        this.saveTasks();
        this.render();
        this.updateStats();
        
        // 清空输入框
        this.elements.taskInput.value = '';
        this.elements.prioritySelect.value = 'medium';
        
        this.showToast('待办事项添加成功', 'success');
    }

    // 切换任务完成状态
    toggleTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (task) {
            task.completed = !task.completed;
            task.completedAt = task.completed ? new Date().toISOString() : null;
            
            this.saveTasks();
            this.render();
            this.updateStats();
            
            const message = task.completed ? '任务已完成' : '任务已标记为未完成';
            this.showToast(message, 'success');
        }
    }

    // 删除任务
    deleteTask(taskId) {
        this.deletingTaskId = taskId;
        this.elements.confirmModal.classList.add('show');
    }

    // 确认删除任务
    confirmDeleteTask() {
        if (this.deletingTaskId) {
            this.tasks = this.tasks.filter(t => t.id !== this.deletingTaskId);
            this.selectedTasks.delete(this.deletingTaskId);
            
            this.saveTasks();
            this.render();
            this.updateStats();
            this.updateBulkActions();
            
            this.showToast('待办事项已删除', 'success');
            this.closeConfirmModal();
        }
    }

    // 编辑任务
    editTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (task) {
            this.editingTaskId = taskId;
            this.elements.editTaskText.value = task.text;
            this.elements.editPriority.value = task.priority;
            this.elements.editModal.classList.add('show');
            this.elements.editTaskText.focus();
        }
    }

    // 处理编辑任务提交
    handleEditTask(e) {
        e.preventDefault();
        
        const text = this.elements.editTaskText.value.trim();
        const priority = this.elements.editPriority.value;
        
        if (!text) {
            this.showToast('请输入待办事项内容', 'error');
            return;
        }
        
        const task = this.tasks.find(t => t.id === this.editingTaskId);
        if (task) {
            task.text = text;
            task.priority = priority;
            
            this.saveTasks();
            this.render();
            this.closeEditModal();
            
            this.showToast('待办事项已更新', 'success');
        }
    }

    // 任务选择
    toggleTaskSelection(taskId) {
        if (this.selectedTasks.has(taskId)) {
            this.selectedTasks.delete(taskId);
        } else {
            this.selectedTasks.add(taskId);
        }
        this.updateBulkActions();
        this.render();
    }

    // 批量完成任务
    completeSelectedTasks() {
        let completedCount = 0;
        this.selectedTasks.forEach(taskId => {
            const task = this.tasks.find(t => t.id === taskId);
            if (task && !task.completed) {
                task.completed = true;
                task.completedAt = new Date().toISOString();
                completedCount++;
            }
        });
        
        if (completedCount > 0) {
            this.saveTasks();
            this.render();
            this.updateStats();
            this.showToast(`已完成 ${completedCount} 个任务`, 'success');
        }
        
        this.cancelSelection();
    }

    // 批量删除任务
    deleteSelectedTasks() {
        const deleteCount = this.selectedTasks.size;
        this.tasks = this.tasks.filter(t => !this.selectedTasks.has(t.id));
        
        this.saveTasks();
        this.render();
        this.updateStats();
        this.showToast(`已删除 ${deleteCount} 个任务`, 'success');
        
        this.cancelSelection();
    }

    // 取消选择
    cancelSelection() {
        this.selectedTasks.clear();
        this.updateBulkActions();
        this.render();
    }

    // 更新批量操作显示
    updateBulkActions() {
        const selectedCount = this.selectedTasks.size;
        if (selectedCount > 0) {
            this.elements.bulkActions.style.display = 'block';
            this.elements.selectedCount.textContent = selectedCount;
        } else {
            this.elements.bulkActions.style.display = 'none';
        }
    }

    // 搜索处理
    handleSearch() {
        this.render();
    }

    // 排序处理
    handleSort() {
        this.currentSort = this.elements.sortSelect.value;
        this.render();
    }

    // 过滤处理
    handleFilter(filter) {
        this.currentFilter = filter;

        // 更新按钮状态
        this.elements.filterButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.filter === filter);
        });

        this.render();
    }

    // 获取过滤后的任务
    getFilteredTasks() {
        let filteredTasks = [...this.tasks];

        // 应用搜索过滤
        const searchTerm = this.elements.searchInput.value.toLowerCase().trim();
        if (searchTerm) {
            filteredTasks = filteredTasks.filter(task =>
                task.text.toLowerCase().includes(searchTerm)
            );
        }

        // 应用状态过滤
        switch (this.currentFilter) {
            case 'completed':
                filteredTasks = filteredTasks.filter(task => task.completed);
                break;
            case 'pending':
                filteredTasks = filteredTasks.filter(task => !task.completed);
                break;
            // 'all' 不需要额外过滤
        }

        // 应用排序
        switch (this.currentSort) {
            case 'newest':
                filteredTasks.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
                break;
            case 'oldest':
                filteredTasks.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt));
                break;
            case 'priority':
                const priorityOrder = { high: 3, medium: 2, low: 1 };
                filteredTasks.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);
                break;
            case 'alphabetical':
                filteredTasks.sort((a, b) => a.text.localeCompare(b.text));
                break;
        }

        return filteredTasks;
    }

    // 渲染任务列表
    render() {
        const filteredTasks = this.getFilteredTasks();

        if (filteredTasks.length === 0) {
            this.elements.tasksList.style.display = 'none';
            this.elements.emptyState.style.display = 'block';
        } else {
            this.elements.tasksList.style.display = 'block';
            this.elements.emptyState.style.display = 'none';

            this.elements.tasksList.innerHTML = filteredTasks.map(task =>
                this.createTaskHTML(task)
            ).join('');

            // 绑定任务项事件
            this.bindTaskEvents();
        }
    }

    // 创建任务HTML
    createTaskHTML(task) {
        const createdDate = new Date(task.createdAt).toLocaleDateString('zh-CN');
        const createdTime = new Date(task.createdAt).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });

        return `
            <div class="task-item ${task.completed ? 'completed' : ''}" data-task-id="${task.id}">
                <input type="checkbox" class="task-select" ${this.selectedTasks.has(task.id) ? 'checked' : ''}>
                <input type="checkbox" class="task-checkbox" ${task.completed ? 'checked' : ''}>
                <div class="task-content">
                    <div class="task-text">${this.escapeHtml(task.text)}</div>
                    <div class="task-meta">
                        <span class="priority-badge priority-${task.priority}">
                            ${this.getPriorityText(task.priority)}
                        </span>
                        <span class="task-date">创建于 ${createdDate} ${createdTime}</span>
                        ${task.completedAt ? `<span class="completed-date">完成于 ${new Date(task.completedAt).toLocaleDateString('zh-CN')}</span>` : ''}
                    </div>
                </div>
                <div class="task-actions">
                    <button class="action-btn edit-btn" title="编辑">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="action-btn delete-btn" title="删除">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }

    // 绑定任务项事件
    bindTaskEvents() {
        // 任务完成状态切换
        document.querySelectorAll('.task-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const taskId = e.target.closest('.task-item').dataset.taskId;
                this.toggleTask(taskId);
            });
        });

        // 任务选择
        document.querySelectorAll('.task-select').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const taskId = e.target.closest('.task-item').dataset.taskId;
                this.toggleTaskSelection(taskId);
            });
        });

        // 编辑按钮
        document.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const taskId = e.target.closest('.task-item').dataset.taskId;
                this.editTask(taskId);
            });
        });

        // 删除按钮
        document.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const taskId = e.target.closest('.task-item').dataset.taskId;
                this.deleteTask(taskId);
            });
        });
    }

    // 更新统计信息
    updateStats() {
        const total = this.tasks.length;
        const completed = this.tasks.filter(t => t.completed).length;
        const pending = total - completed;

        this.elements.totalTasks.textContent = total;
        this.elements.completedTasks.textContent = completed;
        this.elements.pendingTasks.textContent = pending;
    }

    // 保存任务到本地存储
    saveTasks() {
        localStorage.setItem('todoTasks', JSON.stringify(this.tasks));
    }

    // 获取优先级文本
    getPriorityText(priority) {
        const priorityMap = {
            high: '高',
            medium: '中',
            low: '低'
        };
        return priorityMap[priority] || '中';
    }

    // HTML转义
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 关闭编辑模态框
    closeEditModal() {
        this.elements.editModal.classList.remove('show');
        this.editingTaskId = null;
    }

    // 关闭确认删除模态框
    closeConfirmModal() {
        this.elements.confirmModal.classList.remove('show');
        this.deletingTaskId = null;
    }

    // 显示消息提示
    showToast(message, type = 'info') {
        const toast = this.elements.toast;
        const messageElement = toast.querySelector('.toast-message');

        messageElement.textContent = message;
        toast.className = `toast ${type}`;
        toast.classList.add('show');

        // 3秒后自动隐藏
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    }

    // 键盘快捷键处理
    handleKeyboard(e) {
        // Ctrl/Cmd + Enter 快速添加任务
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            if (this.elements.taskInput === document.activeElement) {
                this.elements.addTaskForm.dispatchEvent(new Event('submit'));
            }
        }

        // Escape 关闭模态框
        if (e.key === 'Escape') {
            if (this.elements.editModal.classList.contains('show')) {
                this.closeEditModal();
            }
            if (this.elements.confirmModal.classList.contains('show')) {
                this.closeConfirmModal();
            }
        }

        // Ctrl/Cmd + A 全选任务
        if ((e.ctrlKey || e.metaKey) && e.key === 'a' && !e.target.matches('input, textarea')) {
            e.preventDefault();
            const visibleTasks = this.getFilteredTasks();
            visibleTasks.forEach(task => this.selectedTasks.add(task.id));
            this.updateBulkActions();
            this.render();
        }
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new TodoApp();
});
