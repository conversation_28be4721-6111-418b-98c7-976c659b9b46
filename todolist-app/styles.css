/* 全局样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    background: #f8f9fa;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 2rem;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.logo i {
    font-size: 2.5rem;
    color: #fff;
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    letter-spacing: -1px;
}

.header-stats {
    display: flex;
    gap: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #fff;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 主要内容区域 */
.main-content {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

/* 添加任务区域 */
.add-task-section {
    margin-bottom: 2rem;
}

.add-task-container {
    background: white;
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.input-group {
    display: flex;
    gap: 1rem;
    align-items: center;
}

#taskInput {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

#taskInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.priority-select {
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    background: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.priority-select:focus {
    outline: none;
    border-color: #667eea;
}

.add-btn {
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* 过滤区域 */
.filter-section {
    margin-bottom: 2rem;
}

.filter-container {
    background: white;
    padding: 1.5rem;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 250px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

#searchInput {
    width: 100%;
    padding: 0.8rem 1rem 0.8rem 3rem;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

#searchInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.8rem 1.2rem;
    border: 2px solid #e1e5e9;
    background: white;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.filter-btn:hover {
    border-color: #667eea;
    color: #667eea;
}

.filter-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

#sortSelect {
    padding: 0.8rem;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    background: white;
    cursor: pointer;
    font-size: 0.9rem;
}

/* 任务列表 */
.tasks-section {
    margin-bottom: 2rem;
}

.tasks-container {
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.tasks-list {
    max-height: 600px;
    overflow-y: auto;
}

/* 任务项样式 */
.task-item {
    display: flex;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
}

.task-item:hover {
    background: #f8f9fa;
}

.task-item.completed {
    opacity: 0.7;
    background: #f8f9fa;
}

.task-item.completed .task-text {
    text-decoration: line-through;
    color: #999;
}

.task-checkbox {
    width: 20px;
    height: 20px;
    margin-right: 1rem;
    cursor: pointer;
}

.task-select {
    width: 18px;
    height: 18px;
    margin-right: 1rem;
    cursor: pointer;
}

.task-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.task-text {
    font-size: 1rem;
    line-height: 1.5;
    word-break: break-word;
}

.task-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.8rem;
    color: #666;
}

.priority-badge {
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-high {
    background: #fee;
    color: #e74c3c;
}

.priority-medium {
    background: #fff3cd;
    color: #f39c12;
}

.priority-low {
    background: #d4edda;
    color: #27ae60;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.task-item:hover .task-actions {
    opacity: 1;
}

.action-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.edit-btn {
    background: #3498db;
    color: white;
}

.edit-btn:hover {
    background: #2980b9;
    transform: scale(1.1);
}

.delete-btn {
    background: #e74c3c;
    color: white;
}

.delete-btn:hover {
    background: #c0392b;
    transform: scale(1.1);
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #666;
}

.empty-icon i {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #999;
}

/* 批量操作 */
.bulk-actions {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    padding: 1rem 2rem;
    border-radius: 16px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.bulk-actions-content {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.selected-count {
    font-weight: 600;
    color: #667eea;
}

.bulk-buttons {
    display: flex;
    gap: 1rem;
}

.bulk-btn {
    padding: 0.8rem 1.2rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.complete-selected {
    background: #27ae60;
    color: white;
}

.delete-selected {
    background: #e74c3c;
    color: white;
}

.cancel-selection {
    background: #95a5a6;
    color: white;
}

.bulk-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2000;
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    border-radius: 16px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideIn 0.3s ease;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #f0f0f0;
}

.modal-header h3 {
    font-size: 1.3rem;
    color: #333;
}

.close-btn {
    width: 32px;
    height: 32px;
    border: none;
    background: #f0f0f0;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #e0e0e0;
}

.modal-body {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group textarea,
.form-group select {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    font-family: inherit;
    transition: all 0.3s ease;
}

.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.btn-primary,
.btn-secondary,
.btn-danger {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

/* 消息提示 */
.toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transform: translateX(400px);
    transition: transform 0.3s ease;
    z-index: 3000;
    display: flex;
    align-items: center;
    min-width: 300px;
}

.toast.show {
    transform: translateX(0);
}

.toast-content {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.toast-icon {
    font-size: 1.2rem;
}

.toast.success {
    border-left: 4px solid #27ae60;
}

.toast.success .toast-icon::before {
    content: '✓';
    color: #27ae60;
}

.toast.error {
    border-left: 4px solid #e74c3c;
}

.toast.error .toast-icon::before {
    content: '✕';
    color: #e74c3c;
}

.toast.info {
    border-left: 4px solid #3498db;
}

.toast.info .toast-icon::before {
    content: 'ℹ';
    color: #3498db;
}

/* 动画 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-50px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-stats {
        gap: 1rem;
    }

    .main-content {
        padding: 1rem;
    }

    .input-group {
        flex-direction: column;
    }

    .filter-container {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .task-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .task-actions {
        opacity: 1;
        align-self: flex-end;
    }

    .bulk-actions {
        left: 1rem;
        right: 1rem;
        transform: none;
    }

    .bulk-actions-content {
        flex-direction: column;
        gap: 1rem;
    }

    .toast {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        min-width: auto;
    }
}
